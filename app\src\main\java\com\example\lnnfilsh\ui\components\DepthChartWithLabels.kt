package com.example.lnnfilsh.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lnnfilsh.R
import com.example.lnnfilsh.model.FishData
import com.example.lnnfilsh.ui.theme.AccentBlue
import com.example.lnnfilsh.ui.theme.FishIconYellow
import com.example.lnnfilsh.ui.theme.WaterDeepBlue
import androidx.compose.foundation.clickable

@Composable
fun DepthChartWithLabels(
    depthHistory: List<Float>,
    fishData: List<FishData>,
    currentDepth: Float,
    waterTemperature: Float,
    batteryPercentage: Int,
    isConnected: Boolean,
    ultrasonicData: List<Float> = emptyList(), // 添加超声波数据参数
    onConnectToggle: () -> Unit = {}, // 添加连接状态切换回调
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.fillMaxSize()) {
        // 深度图表 - 全屏显示，不使用默认值，完全由服务器数据决定
        DepthChart(
            depthHistory = depthHistory,
            fishData = fishData,
            ultrasonicData = ultrasonicData, // 传递超声波数据
            modifier = Modifier.fillMaxSize()
        )
        
        // 顶部状态信息卡片 - 整合应用标题和数据状态
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 0.dp, top = 0.dp, end = 0.dp, bottom = 0.dp) // 移除所有内边距
                .alpha(0.9f) // 增加一点透明度使其更醒目
                .align(Alignment.TopCenter),
            shape = RoundedCornerShape(0.dp, 0.dp, 16.dp, 16.dp), // 只保留底部圆角
            colors = CardDefaults.cardColors(
                containerColor = AccentBlue.copy(alpha = 0.9f) // 使用主题色，增加视觉识别度
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 4.dp
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 应用标题 - 固定宽度
                Text(
                    text = "老王探鱼器",
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    ),
                    color = Color.White,
                    modifier = Modifier.width(120.dp)
                )
                
                // 右侧状态信息容器 - 均匀分布
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    // 水深信息 - 固定宽度
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.width(80.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_depth),
                            contentDescription = "水深",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            // 确保数据有效，避免显示0
                            text = if (currentDepth > 0) String.format("%.1fm", currentDepth/100) else "- m",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            ),
                            color = Color.White,
                            maxLines = 1
                        )
                    }
                    
                    // 水温信息 - 固定宽度
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.width(90.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_temperature),
                            contentDescription = "水温",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = String.format("%.1f°C", waterTemperature),
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            ),
                            color = FishIconYellow,
                            maxLines = 1
                        )
                    }
                    
                    // 鱼群数量 - 固定宽度
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.width(70.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_fish),
                            contentDescription = "鱼群",
                            tint = FishIconYellow,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "${fishData.size}条",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            ),
                            color = FishIconYellow,
                            maxLines = 1
                        )
                    }
                    
                    // 电池状态 - 固定宽度
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.width(70.dp)
                    ) {
                        // 根据电池百分比显示不同的电池图标
                        val batteryIconId = when {
                            batteryPercentage <= 15 -> R.drawable.ic_battery_low
                            batteryPercentage <= 30 -> R.drawable.ic_battery_25
                            batteryPercentage <= 60 -> R.drawable.ic_battery_50
                            batteryPercentage <= 85 -> R.drawable.ic_battery_75
                            else -> R.drawable.ic_battery_full
                        }
                        
                        val batteryColor = when {
                            batteryPercentage <= 15 -> Color.Red
                            batteryPercentage <= 30 -> Color(0xFFFF9800) // 橙色
                            else -> Color.Green
                        }
                        
                        Icon(
                            painter = painterResource(id = batteryIconId),
                            contentDescription = "电池 $batteryPercentage%",
                            tint = batteryColor,
                            modifier = Modifier.size(20.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(4.dp))
                        
                        Text(
                            text = "$batteryPercentage%",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            ),
                            color = batteryColor,
                            maxLines = 1
                        )
                    }
                    
                    // 连接状态 - 固定宽度
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .width(80.dp)
                            .clickable { onConnectToggle() } // 添加点击事件
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_wifi),
                            contentDescription = "连接状态",
                            tint = if (isConnected) Color.Green else Color.Red,
                            modifier = Modifier.size(20.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(4.dp))
                        
                        Text(
                            text = if (isConnected) "已连接" else "未连接",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            ),
                            color = if (isConnected) Color.Green else Color.Red,
                            maxLines = 1
                        )
                    }
                }
            }
        }
    }
} 