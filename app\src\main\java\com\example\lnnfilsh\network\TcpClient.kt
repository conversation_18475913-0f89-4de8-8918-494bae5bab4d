package com.example.lnnfilsh.network

import android.util.Log
import com.example.lnnfilsh.model.Command
import com.example.lnnfilsh.model.CommandResponse
import com.example.lnnfilsh.model.DeviceStatus
import com.example.lnnfilsh.model.SensorData
import com.example.lnnfilsh.model.SensorDataType
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.io.InputStream
import java.io.OutputStream
import java.net.InetSocketAddress
import java.net.Socket
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.experimental.and
import kotlin.experimental.or

/**
 * TcpClient 类负责与ESP8266设备建立TCP连接并处理数据交换
 */
class TcpClient {
    private val TAG = "TcpClient"
    private val gson = Gson()
    
    // 默认ESP8266 SoftAP地址和端口
    private val serverAddress = "**************"
    private val serverPort = 8266 // 更新为标准端口
    
    private var socket: Socket? = null
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null
    
    private var connectionJob: Job? = null
    private var heartbeatJob: Job? = null
    private var receiveJob: Job? = null
    
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    
    // 数据流，用于向UI传递数据
    private val _sensorDataFlow = MutableSharedFlow<SensorData>(replay = 0)
    val sensorDataFlow: SharedFlow<SensorData> = _sensorDataFlow
    
    private val _deviceStatusFlow = MutableSharedFlow<DeviceStatus>()
    val deviceStatusFlow: SharedFlow<DeviceStatus> = _deviceStatusFlow
    
    private val _commandResponseFlow = MutableSharedFlow<CommandResponse>()
    val commandResponseFlow: SharedFlow<CommandResponse> = _commandResponseFlow
    
    private val _connectionStatusFlow = MutableSharedFlow<Boolean>(replay = 1)
    val connectionStatusFlow: SharedFlow<Boolean> = _connectionStatusFlow
    
    private var isConnected = false
    private var reconnectAttempts = 0
    private val maxReconnectAttempts = 5  // 减少重连次数，避免无限重连
    private var connectionTimeout = 3000   // 减少连接超时时间(毫秒)
    
    // 数据帧格式常量
    companion object {
        // 帧头
        const val FRAME_HEADER_1: Byte = 0xAA.toByte()
        const val FRAME_HEADER_2: Byte = 0xBB.toByte()
        const val CMD_HEADER_1: Byte = 0xCC.toByte()
        const val CMD_HEADER_2: Byte = 0xDD.toByte()
        
        // 命令类型
        const val CMD_START_COLLECTING: Byte = 0x01
        const val CMD_STOP_COLLECTING: Byte = 0x02
        const val CMD_SET_PARAMS: Byte = 0x03
        const val CMD_TOGGLE_LED: Byte = 0x04
        const val CMD_RESET_DEVICE: Byte = 0x05
        const val CMD_MOVE_FORWARD: Byte = 0x06
        const val CMD_MOVE_BACKWARD: Byte = 0x07
        const val CMD_TURN_LEFT: Byte = 0x08
        const val CMD_TURN_RIGHT: Byte = 0x09
        const val CMD_STOP: Byte = 0x0A
        
        // 响应类型
        const val RESP_DEPTH_DATA: Byte = 0x01
        const val RESP_STATUS_DATA: Byte = 0x02
        const val RESP_FISH_DATA: Byte = 0x03
        const val RESP_COMMAND_ACK: Byte = 0x04
        const val RESP_ULTRASONIC_DATA: Byte = 0x05  // 新增：超声波数据类型
        
        // 帧大小
        const val RESPONSE_FRAME_SIZE = 20
        const val COMMAND_FRAME_SIZE = 10
    }
    
    // 连接到ESP8266
    fun connect(ipAddress: String = serverAddress, port: Int = serverPort) {
        if (isConnected) return
        
        // 取消之前的连接任务
        connectionJob?.cancel()
        
        connectionJob = coroutineScope.launch {
            try {
                withTimeoutOrNull(connectionTimeout.toLong()) {
                    withContext(Dispatchers.IO) {
                        Log.d(TAG, "正在连接到 $ipAddress:$port")
                        
                        // 确保之前的连接已关闭
                        closeConnection()
                        
                        socket = Socket()
                        socket?.connect(InetSocketAddress(ipAddress, port), connectionTimeout)
                        socket?.soTimeout = 5000  // 设置读取超时
                        socket?.keepAlive = true  // 启用TCP保活
                        socket?.tcpNoDelay = true // 禁用Nagle算法，减少延迟
                        
                        inputStream = socket?.getInputStream()
                        outputStream = socket?.getOutputStream()
                        
                        isConnected = true
                        reconnectAttempts = 0
                        _connectionStatusFlow.emit(true)
                        
                        Log.d(TAG, "连接成功")
                        
                        // 发送初始重置命令
                        sendBinaryCommand(CMD_RESET_DEVICE, ByteArray(4))
                        delay(300)  // 等待设备重置
                        
                        // 启动心跳包和接收数据的协程
                        startHeartbeat()
                        startReceiving()
                    }
                } ?: run {
                    // 连接超时
                    Log.e(TAG, "连接超时")
                    isConnected = false
                    _connectionStatusFlow.emit(false)
                    handleConnectionError()
                }
            } catch (e: Exception) {
                Log.e(TAG, "连接失败: ${e.message}")
                isConnected = false
                _connectionStatusFlow.emit(false)
                
                // 尝试重连
                handleConnectionError()
            }
        }
    }
    
    // 心跳包机制，保持连接活跃
    private fun startHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = coroutineScope.launch {
            while (isActive && isConnected) {
                try {
                    // 每15秒发送一次心跳包（重置命令）
                    delay(15000)
                    if (isConnected) {
                        val success = sendBinaryCommand(CMD_RESET_DEVICE, ByteArray(4))
                        if (!success) {
                            Log.e(TAG, "心跳包发送失败")
                            handleConnectionError()
                            break
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "发送心跳包失败: ${e.message}")
                    handleConnectionError()
                    break
                }
            }
        }
    }
    
    // 关闭连接
    fun disconnect() {
        connectionJob?.cancel()
        heartbeatJob?.cancel()
        receiveJob?.cancel()
        
        coroutineScope.launch {
            try {
                // 发送停止采集命令
                if (isConnected) {
                    sendBinaryCommand(CMD_STOP_COLLECTING, ByteArray(4))
                    delay(200) // 等待命令发送完成
                }
                
                closeConnection()
            } catch (e: Exception) {
                Log.e(TAG, "关闭连接出错: ${e.message}")
            } finally {
                isConnected = false
                _connectionStatusFlow.emit(false)
            }
        }
    }
    
    // 安全关闭连接资源
    private fun closeConnection() {
        try {
            inputStream?.close()
            outputStream?.close()
            socket?.close()
            
            inputStream = null
            outputStream = null
            socket = null
        } catch (e: Exception) {
            Log.e(TAG, "关闭连接资源出错: ${e.message}")
        }
    }
    
    // 发送二进制命令
    fun sendBinaryCommand(cmdType: Byte, params: ByteArray): Boolean {
        if (!isConnected || outputStream == null) {
            Log.e(TAG, "未连接，无法发送命令")
            return false
        }
        
        try {
            // 构建命令帧
            val buffer = ByteBuffer.allocate(COMMAND_FRAME_SIZE)
            buffer.order(ByteOrder.LITTLE_ENDIAN)
            
            // 帧头
            buffer.put(CMD_HEADER_1)
            buffer.put(CMD_HEADER_2)
            
            // 命令类型
            buffer.put(cmdType)
            
            // 参数长度
            buffer.put(params.size.toByte())
            
            // 参数内容 (最多4字节)
            buffer.put(params.copyOf(minOf(params.size, 4)))
            
            // 填充剩余参数空间
            for (i in params.size until 4) {
                buffer.put(0)
            }
            
            // 计算校验和
            val crc = calculateCRC16(buffer.array(), 2, 6)
            buffer.position(8)
            buffer.put(crc[0])
            buffer.put(crc[1])
            
            // 发送命令
            outputStream?.write(buffer.array())
            outputStream?.flush()
            
            Log.d(TAG, "发送二进制命令: 类型=$cmdType, 参数长度=${params.size}")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "发送二进制命令出错: ${e.message}")
            // 不要立即断开连接，仅在心跳包或数据接收失败时断开
            // handleConnectionError()
            return false
        }
    }
    
    // 发送命令（兼容旧版本）
    fun sendCommand(command: Command) {
        if (!isConnected) {
            Log.e(TAG, "未连接，无法发送命令")
            return
        }
        
        coroutineScope.launch {
            try {
                var success = false
                
                when (command.type) {
                    "startCollecting" -> success = sendBinaryCommand(CMD_START_COLLECTING, ByteArray(4))
                    "stopCollecting" -> success = sendBinaryCommand(CMD_STOP_COLLECTING, ByteArray(4))
                    "toggleLed" -> success = toggleLed() // 使用带重试机制的方法
                    "resetDevice" -> success = sendBinaryCommand(CMD_RESET_DEVICE, ByteArray(4))
                    "moveForward" -> success = sendBinaryCommand(CMD_MOVE_FORWARD, ByteArray(4))
                    "moveBackward" -> success = sendBinaryCommand(CMD_MOVE_BACKWARD, ByteArray(4))
                    "turnLeft" -> success = sendBinaryCommand(CMD_TURN_LEFT, ByteArray(4))
                    "turnRight" -> success = sendBinaryCommand(CMD_TURN_RIGHT, ByteArray(4))
                    "stop" -> success = sendBinaryCommand(CMD_STOP, ByteArray(4))
                    "setParams" -> {
                        val params = ByteArray(4)
                        params[0] = (command.params?.get("sensitivity") as? Number)?.toByte() ?: 5
                        params[1] = (command.params?.get("samplingInterval") as? Number)?.toByte() ?: 50
                        
                        // 使用带重试机制的方法
                        val sensitivity = params[0].toInt().coerceIn(1, 10)
                        val interval = params[1].toInt().coerceIn(1, 100)
                        success = setParameters(sensitivity, interval)
                    }
                    else -> {
                        // 旧版本兼容代码
                        try {
                            val commandJson = gson.toJson(command)
                            outputStream?.write(commandJson.toByteArray())
                            outputStream?.flush()
                            Log.d(TAG, "发送JSON命令: $commandJson")
                            success = true
                        } catch (e: Exception) {
                            Log.e(TAG, "发送JSON命令出错: ${e.message}")
                            success = false
                        }
                    }
                }
                
                if (!success) {
                    Log.e(TAG, "命令 ${command.type} 发送失败")
                    // 不再因为单个命令失败就断开连接
                    // handleConnectionError()
                }
            } catch (e: Exception) {
                Log.e(TAG, "发送命令出错: ${e.message}")
                // 不再因为单个命令失败就断开连接
                // handleConnectionError()
            }
        }
    }
    
    // 开始采集
    fun startCollecting(): Boolean {
        return sendBinaryCommand(CMD_START_COLLECTING, ByteArray(4))
    }
    
    // 停止采集
    fun stopCollecting(): Boolean {
        return sendBinaryCommand(CMD_STOP_COLLECTING, ByteArray(4))
    }
    
    // 设置参数
    fun setParameters(sensitivity: Int, samplingInterval: Int): Boolean {
        val params = ByteArray(4)
        params[0] = sensitivity.coerceIn(1, 10).toByte()
        params[1] = samplingInterval.coerceIn(1, 100).toByte()
        
        // 重试最多3次
        for (i in 0 until 3) {
            val success = sendBinaryCommand(CMD_SET_PARAMS, params)
            if (success) {
                return true
            }
            // 短暂延迟后重试
            Thread.sleep(100)
        }
        return false
    }
    
    // 切换LED灯
    fun toggleLed(): Boolean {
        // 重试最多3次
        for (i in 0 until 3) {
            val success = sendBinaryCommand(CMD_TOGGLE_LED, ByteArray(4))
            if (success) {
                return true
            }
            // 短暂延迟后重试
            Thread.sleep(100)
        }
        return false
    }
    
    // 重置设备
    fun resetDevice(): Boolean {
        return sendBinaryCommand(CMD_RESET_DEVICE, ByteArray(4))
    }
    
    // 启动数据接收
    private fun startReceiving() {
        receiveJob?.cancel()
        receiveJob = coroutineScope.launch {
            val buffer = ByteArray(1024)
            var accumulatedData = ByteArray(0)
            
            while (isActive && isConnected) {
                try {
                    val bytesRead = withTimeoutOrNull(10000) { // 10秒读取超时
                        inputStream?.read(buffer)
                    } ?: -1
                    
                    if (bytesRead == -1) {
                        // 连接已关闭
                        Log.e(TAG, "连接已关闭或读取超时")
                        handleConnectionError()
                        break
                    } else if (bytesRead > 0) {
                        val data = buffer.copyOfRange(0, bytesRead)
                        
                        // 合并之前未处理完的数据
                        accumulatedData = accumulatedData + data
                        
                        // 处理完整数据帧
                        accumulatedData = processDataFrames(accumulatedData)
                        
                        // 防止数据积累过多
                        if (accumulatedData.size > 4096) {
                            Log.w(TAG, "数据缓冲区过大，清空数据")
                            accumulatedData = ByteArray(0)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "接收数据出错: ${e.message}")
                    handleConnectionError()
                    break
                }
            }
        }
    }
    
    // 处理可能包含多个数据帧的数据
    private fun processDataFrames(data: ByteArray): ByteArray {
        if (data.size < 2) return data // 数据太少，无法处理
        
        var remainingData = data
        
        // 尝试找到并处理所有完整的数据帧
        while (remainingData.size >= RESPONSE_FRAME_SIZE) {
            // 查找帧头
            var frameStartIndex = -1
            for (i in 0..remainingData.size - 2) {
                if (remainingData[i] == FRAME_HEADER_1 && remainingData[i + 1] == FRAME_HEADER_2) {
                    frameStartIndex = i
                    break
                }
            }
            
            if (frameStartIndex == -1) {
                // 没有找到帧头，保留最后一个字节以防它是帧头的一部分
                return if (remainingData.isNotEmpty()) {
                    byteArrayOf(remainingData.last())
                } else {
                    ByteArray(0)
                }
            }
            
            if (frameStartIndex + RESPONSE_FRAME_SIZE > remainingData.size) {
                // 找到帧头但数据不完整，返回从帧头开始的数据等待更多数据
                return remainingData.copyOfRange(frameStartIndex, remainingData.size)
            }
            
            // 提取一个完整帧
            val frameData = remainingData.copyOfRange(frameStartIndex, frameStartIndex + RESPONSE_FRAME_SIZE)
            
            // 处理这个数据帧
            try {
                processReceivedData(frameData)
            } catch (e: Exception) {
                Log.e(TAG, "处理数据帧出错: ${e.message}")
            }
            
            // 移除已处理的数据
            remainingData = if (frameStartIndex + RESPONSE_FRAME_SIZE < remainingData.size) {
                remainingData.copyOfRange(frameStartIndex + RESPONSE_FRAME_SIZE, remainingData.size)
            } else {
                ByteArray(0)
            }
        }
        
        // 返回未处理完的数据，等待更多数据到达
        return remainingData
    }
    
    // 处理接收到的数据
    private fun processReceivedData(data: ByteArray) {
        try {
            // 检查数据帧格式
            if (data.size >= RESPONSE_FRAME_SIZE && 
                data[0] == FRAME_HEADER_1 && 
                data[1] == FRAME_HEADER_2) {
                
                try {
                    // 提取命令类型和数据长度
                    val cmdType = data[2]
                    val dataLength = data[3]
                    
                    // 验证数据长度
                    if (dataLength < 0 || dataLength > 16) {
                        Log.e(TAG, "无效的数据长度: $dataLength")
                        return
                    }
                    
                    // 验证CRC校验和
                    if (data.size < RESPONSE_FRAME_SIZE) {
                        Log.e(TAG, "数据长度不足，无法验证CRC")
                        return
                    }
                    
                    val receivedCRC = ByteArray(2)
                    System.arraycopy(data, RESPONSE_FRAME_SIZE - 2, receivedCRC, 0, 2)
                    
                    val calculatedCRC = calculateCRC16(data, 2, 16)
                    if (!receivedCRC.contentEquals(calculatedCRC)) {
                        Log.e(TAG, "CRC校验失败")
                        return
                    }
                    
                    // 根据命令类型处理数据
                    when (cmdType) {
                        RESP_DEPTH_DATA -> {
                            try {
                                // 确保数据长度足够
                                if (data.size < 8) {
                                    Log.e(TAG, "深度数据长度不足")
                                    return
                                }
                                
                                // 解析深度数据
                                val buffer = ByteBuffer.wrap(data, 4, 4)
                                buffer.order(ByteOrder.LITTLE_ENDIAN)
                                
                                val depth = buffer.short.toInt() and 0xFFFF
                                val temperature = buffer.short.toInt() and 0xFFFF
                                
                                // 数据合理性验证
                                if (depth < 0 || depth > 2000 || temperature < 0 || temperature > 700) {
                                    Log.e(TAG, "接收到无效的深度或温度数据: 深度=$depth, 温度=$temperature")
                                    return
                                }
                                
                                val sensorData = SensorData(
                                    type = SensorDataType.DEPTH,
                                    timestamp = System.currentTimeMillis(),
                                    values = mapOf(
                                        "depth" to depth.toFloat(),
                                        "temperature" to (temperature / 10f)
                                    )
                                )
                                
                                coroutineScope.launch {
                                    _sensorDataFlow.emit(sensorData)
                                }
                                
                                Log.d(TAG, "接收深度数据: 深度=${depth}cm, 温度=${temperature/10}°C")
                            } catch (e: Exception) {
                                Log.e(TAG, "处理深度数据时出错: ${e.message}", e)
                            }
                        }
                        
                        RESP_STATUS_DATA -> {
                            try {
                                // 确保数据长度足够
                                if (data.size < 6) {
                                    Log.e(TAG, "状态数据长度不足")
                                    return
                                }
                                
                                // 解析状态数据
                                val batteryPercentage = data[4].toInt() and 0xFF
                                val statusByte = data[5]
                                
                                // 数据合理性验证
                                if (batteryPercentage < 0 || batteryPercentage > 100) {
                                    Log.e(TAG, "无效的电池百分比: $batteryPercentage")
                                    return
                                }
                                
                                val isCollecting = (statusByte and 0x01.toByte()) != 0.toByte()
                                val isLedOn = (statusByte and 0x02.toByte()) != 0.toByte()
                                
                                val deviceStatus = DeviceStatus(
                                    batteryPercentage = batteryPercentage,
                                    isCollecting = isCollecting,
                                    ledStatus = isLedOn
                                )
                                
                                coroutineScope.launch {
                                    _deviceStatusFlow.emit(deviceStatus)
                                }
                                
                                Log.d(TAG, "接收状态数据: 电量=${batteryPercentage}%, 采集=${isCollecting}, LED=${isLedOn}")
                            } catch (e: Exception) {
                                Log.e(TAG, "处理状态数据时出错: ${e.message}", e)
                            }
                        }
                        
                        RESP_FISH_DATA -> {
                            try {
                                // 确保数据长度足够
                                if (data.size < 8) {
                                    Log.e(TAG, "鱼群数据长度不足")
                                    return
                                }
                                
                                // 解析鱼群数据
                                val buffer = ByteBuffer.wrap(data, 4, 4)
                                buffer.order(ByteOrder.LITTLE_ENDIAN)
                                
                                val fishDepth = buffer.short.toInt() and 0xFFFF
                                val fishSize = data[6].toInt() and 0xFF
                                val confidence = data[7].toInt() and 0xFF
                                
                                // 数据合理性验证
                                if (fishDepth < 0 || fishDepth > 2000 || fishSize < 1 || fishSize > 3 || confidence < 0 || confidence > 100) {
                                    Log.e(TAG, "接收到无效的鱼群数据: 深度=$fishDepth, 大小=$fishSize, 置信度=$confidence")
                                    return
                                }
                                
                                val sensorData = SensorData(
                                    type = SensorDataType.FISH,
                                    timestamp = System.currentTimeMillis(),
                                    values = mapOf(
                                        "depth" to fishDepth.toFloat(),
                                        "size" to fishSize.toFloat(),
                                        "confidence" to confidence.toFloat()
                                    )
                                )
                                
                                coroutineScope.launch {
                                    _sensorDataFlow.emit(sensorData)
                                }
                                
                                Log.d(TAG, "接收鱼群数据: 深度=${fishDepth}cm, 大小=$fishSize, 置信度=$confidence%")
                            } catch (e: Exception) {
                                Log.e(TAG, "处理鱼群数据时出错: ${e.message}", e)
                            }
                        }
                        
                        RESP_ULTRASONIC_DATA -> {
                            try {
                                // 确保数据长度足够
                                if (data.size < 12) {
                                    Log.e(TAG, "超声波数据长度不足")
                                    return
                                }
                                
                                // 解析超声波探测数据
                                val ultrasonicData = FloatArray(8)
                                for (i in 0 until 8) {
                                    // 每个点占一个字节，每单位5厘米
                                    val rawValue = data[4 + i].toInt() and 0xFF
                                    
                                    // 数据合理性验证
                                    if (rawValue < 0 || rawValue > 200) { // 最大1000厘米(10米)
                                        Log.e(TAG, "无效的超声波数据点: $rawValue")
                                        ultrasonicData[i] = 0f
                                    } else {
                                        ultrasonicData[i] = rawValue * 5.0f  // 转换为厘米
                                    }
                                }
                                
                                // 构建数据点映射
                                val dataPoints = mutableMapOf<String, Float>()
                                for (i in ultrasonicData.indices) {
                                    dataPoints["point$i"] = ultrasonicData[i]
                                }
                                
                                val sensorData = SensorData(
                                    type = SensorDataType.ULTRASONIC,  // 使用专用超声波类型
                                    timestamp = System.currentTimeMillis(),
                                    values = dataPoints
                                )
                                
                                coroutineScope.launch {
                                    _sensorDataFlow.emit(sensorData)
                                }
                                
                                Log.d(TAG, "接收超声波探测数据: ${ultrasonicData.joinToString { "${it/100}m" }}")
                            } catch (e: Exception) {
                                Log.e(TAG, "处理超声波数据时出错: ${e.message}", e)
                            }
                        }
                        
                        RESP_COMMAND_ACK -> {
                            try {
                                // 确保数据长度足够
                                if (data.size < 5) {
                                    Log.e(TAG, "命令响应数据长度不足")
                                    return
                                }
                                
                                // 解析命令响应
                                val originalCmd = data[4].toInt() and 0xFF
                                
                                val response = CommandResponse(
                                    success = true,
                                    commandType = originalCmd,
                                    message = "命令执行成功"
                                )
                                
                                coroutineScope.launch {
                                    _commandResponseFlow.emit(response)
                                }
                                
                                Log.d(TAG, "接收命令响应: 原命令类型=$originalCmd")
                            } catch (e: Exception) {
                                Log.e(TAG, "处理命令响应时出错: ${e.message}", e)
                            }
                        }
                        
                        else -> {
                            Log.w(TAG, "未知的命令类型: ${cmdType.toInt() and 0xFF}")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析数据帧时出错: ${e.message}", e)
                }
            } else {
                // 尝试解析旧版本JSON数据
                try {
                    val responseStr = String(data)
                    if (responseStr.startsWith("{") && responseStr.endsWith("}")) {
                        // 处理JSON格式数据
                        processJsonData(responseStr)
                    } else {
                        Log.d(TAG, "非JSON格式数据: ${responseStr.take(50)}${if (responseStr.length > 50) "..." else ""}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "无法解析的数据格式: ${e.message}", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理接收数据时出现未预期错误: ${e.message}", e)
        }
    }
    
    // 处理JSON格式数据（旧版兼容）
    private fun processJsonData(jsonData: String) {
        try {
            // 尝试解析为命令响应
            try {
                val commandResponse = gson.fromJson(jsonData, CommandResponse::class.java)
                if (commandResponse != null) {
                    coroutineScope.launch {
                        _commandResponseFlow.emit(commandResponse)
                    }
                    return
                }
            } catch (e: Exception) {
                Log.e(TAG, "解析为命令响应失败: ${e.message}")
            }
            
            // 尝试解析为设备状态
            try {
                val deviceStatus = gson.fromJson(jsonData, DeviceStatus::class.java)
                if (deviceStatus != null) {
                    // 验证设备状态数据
                    val validBatteryPercentage = deviceStatus.batteryPercentage.coerceIn(0, 100)
                    val validDeviceStatus = DeviceStatus(
                        batteryPercentage = validBatteryPercentage,
                        isCollecting = deviceStatus.isCollecting,
                        ledStatus = deviceStatus.ledStatus
                    )
                    
                    coroutineScope.launch {
                        _deviceStatusFlow.emit(validDeviceStatus)
                    }
                    return
                }
            } catch (e: Exception) {
                Log.e(TAG, "解析为设备状态失败: ${e.message}")
            }
            
            // 尝试解析为传感器数据
            try {
                val sensorData = gson.fromJson(jsonData, SensorData::class.java)
                if (sensorData != null) {
                    // 验证传感器数据
                    val validValue = if (sensorData.value < 0 || sensorData.value > 2000) 0f else sensorData.value
                    val validValues = sensorData.values.mapValues { (_, value) -> 
                        if (value < 0 || value > 2000) 0f else value 
                    }
                    
                    val validSensorData = SensorData(
                        type = sensorData.type,
                        timestamp = sensorData.timestamp,
                        values = validValues,
                        value = validValue
                    )
                    
                    coroutineScope.launch {
                        _sensorDataFlow.emit(validSensorData)
                    }
                    return
                }
            } catch (e: Exception) {
                Log.e(TAG, "解析为传感器数据失败: ${e.message}")
            }
            
            Log.e(TAG, "无法解析的JSON数据: $jsonData")
        } catch (e: Exception) {
            Log.e(TAG, "处理JSON数据出错: ${e.message}", e)
        }
    }
    
    // 处理连接错误
    private fun handleConnectionError() {
        if (!isConnected) return
        
        isConnected = false
        coroutineScope.launch {
            _connectionStatusFlow.emit(false)
        }
        
        // 清理资源
        closeConnection()
        
        // 在心跳或数据接收失败时尝试重连
        reconnectAttempts++
        if (reconnectAttempts < maxReconnectAttempts) {
            coroutineScope.launch {
                val delayTime = minOf(2000 * reconnectAttempts, 10000)  // 指数退避，最多10秒
                Log.d(TAG, "将在 ${delayTime/1000} 秒后尝试第 $reconnectAttempts 次重连")
                delay(delayTime.toLong()) // 延迟重试
                connect()
            }
        }
    }
    
    // 计算CRC16-MODBUS校验和
    private fun calculateCRC16(data: ByteArray, offset: Int, length: Int): ByteArray {
        var crc = 0xFFFF
        
        for (i in offset until offset + length) {
            crc = crc xor (data[i].toInt() and 0xFF)
            
            for (j in 0 until 8) {
                if ((crc and 0x0001) != 0) {
                    crc = (crc ushr 1) xor 0xA001
                } else {
                    crc = crc ushr 1
                }
            }
        }
        
        val result = ByteArray(2)
        result[0] = (crc and 0xFF).toByte()
        result[1] = ((crc ushr 8) and 0xFF).toByte()
        
        return result
    }
} 