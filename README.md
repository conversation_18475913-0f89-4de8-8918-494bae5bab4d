# 鱼探仪应用说明文档

## 应用概述

"鱼探仪"是一款基于Android的移动应用，用于与水下声纳探测设备连接，实时显示水下地形、鱼群分布和相关数据。该应用采用现代化UI设计，具有全屏沉浸式体验，透明悬浮控制面板，以及高度可视化的数据展示功能。

## 技术架构

### 开发环境
- **开发语言**：Kotlin
- **UI框架**：Jetpack Compose
- **架构模式**：MVVM (Model-View-ViewModel)

### 核心组件

1. **网络连接模块**
   - TCP/IP通信：通过TcpClient实现与硬件设备的数据交换
   - 蓝牙连接：支持与蓝牙设备配对和数据传输

2. **数据处理模块**
   - 传感器数据处理：解析超声波传感器、温度传感器等数据
   - 鱼群识别算法：基于深度变化和灵敏度分析识别水下鱼群

3. **UI渲染模块**
   - 水域深度图表：动态绘制水下地形和深度标尺
   - 鱼群标记：根据识别数据在界面上标记不同大小的鱼群
   - 悬浮控制面板：半透明交互界面，适应不同屏幕方向

## 通信协议

### 连接方式

#### Wi-Fi 连接
- **连接方式**：应用通过TCP/IP协议与设备建立Socket连接
- **设备配置**：
  * 默认IP地址：`***********`（ESP8266 AP模式）
  * 默认端口：`8266`
  * SSID格式：`FishFinder_XXXX`（XXXX为设备唯一标识）
  * 默认密码：`12345678`
- **连接流程**：
  1. 手机连接到设备创建的Wi-Fi热点
  2. 应用自动尝试连接到默认IP和端口
  3. 连接成功后建立Socket通信通道

#### 蓝牙连接
- **连接标准**：蓝牙4.0 BLE（低功耗蓝牙）
- **服务UUID**：`0000FFE0-0000-1000-8000-00805F9B34FB`
- **特征UUID**：`0000FFE1-0000-1000-8000-00805F9B34FB`
- **连接流程**：
  1. 应用扫描周围蓝牙设备
  2. 识别名称前缀为`FishFinder_`的设备
  3. 建立GATT连接
  4. 订阅通知以接收数据

### 数据帧结构

#### 上行数据帧（设备→应用）
数据帧采用二进制格式，每帧固定长度为20字节：

| 字段         | 字节范围 | 长度 | 说明                                 |
|-------------|----------|------|-------------------------------------|
| 帧头         | 0-1      | 2    | 固定值：0xAA 0xBB                    |
| 命令类型     | 2        | 1    | 0x01: 深度数据<br>0x02: 状态数据<br>0x03: 鱼群数据<br>0x04: 响应命令<br>0x05: 超声波数据 |
| 数据长度     | 3        | 1    | 有效数据字节数                        |
| 数据内容     | 4-17     | 14   | 根据命令类型不同而变化的数据内容       |
| 校验和       | 18-19    | 2    | CRC16校验码，对字节2-17计算           |

**深度数据（0x01）格式**：
```
数据内容[0-1]: 当前深度（厘米，uint16）
数据内容[2-3]: 水温（0.1℃，uint16）
数据内容[4-13]: 保留
```

**状态数据（0x02）格式**：
```
数据内容[0]: 电池电量（百分比，uint8）
数据内容[1]: 设备状态（位标志：bit0-采集状态 bit1-LED状态 bit2-错误标志 bit3-前进 bit4-后退 bit5-左转 bit6-右转）
数据内容[2-13]: 保留
```

**鱼群数据（0x03）格式**：
```
数据内容[0-1]: 鱼的深度（厘米，uint16）
数据内容[2]: 鱼的大小等级（1-小 2-中 3-大，uint8）
数据内容[3]: 鱼的置信度（百分比，uint8）
数据内容[4-13]: 保留
```

**超声波数据（0x05）格式**：
```
数据内容[0-1]: 点1深度（厘米，uint16）
数据内容[2-3]: 点2深度（厘米，uint16）
数据内容[4-5]: 点3深度（厘米，uint16）
数据内容[6-7]: 点4深度（厘米，uint16）
数据内容[8-9]: 点5深度（厘米，uint16）
数据内容[10-11]: 点6深度（厘米，uint16）
数据内容[12-13]: 保留
```

#### 下行数据帧（应用→设备）
命令帧采用二进制格式，固定长度为10字节：

| 字段         | 字节范围 | 长度 | 说明                                 |
|-------------|----------|------|-------------------------------------|
| 帧头         | 0-1      | 2    | 固定值：0xCC 0xDD                    |
| 命令类型     | 2        | 1    | 0x01: 开始采集<br>0x02: 停止采集<br>0x03: 设置参数<br>0x04: 切换LED<br>0x05: 重置设备<br>0x06: 前进<br>0x07: 后退<br>0x08: 左转<br>0x09: 右转<br>0x0A: 停止 |
| 参数长度     | 3        | 1    | 参数字节数                            |
| 参数内容     | 4-7      | 4    | 根据命令类型不同而变化的参数内容       |
| 校验和       | 8-9      | 2    | CRC16校验码，对字节2-7计算             |

**设置参数（0x03）格式**：
```
参数内容[0]: 灵敏度（1-10，uint8）
参数内容[1]: 采样间隔（毫秒/10，uint8）
参数内容[2-3]: 保留
```

**方向控制命令（0x06-0x0A）格式**：
```
参数内容[0]: 速度（1-10，uint8，仅适用于移动命令）
参数内容[1-3]: 保留
```

### 通信流程

1. **连接建立**：
   - 应用发送连接请求命令（0x05）
   - 设备回复状态数据（0x02）确认连接

2. **数据采集**：
   - 应用发送开始采集命令（0x01）
   - 设备按照设定的采样间隔发送深度数据（0x01）
   - 当检测到鱼时，设备发送鱼群数据（0x03）
   - 设备定期发送超声波数据（0x05）
   - 设备定期（每5秒）发送状态数据（0x02）

3. **控制命令**：
   - 应用可随时发送各类控制命令（LED控制、参数设置等）
   - 设备收到命令后执行相应操作并回复响应命令（0x04）
   - 方向控制命令发送后持续生效，直到收到停止命令（0x0A）

4. **断开连接**：
   - 应用发送停止采集命令（0x02）
   - 应用关闭Socket连接或断开蓝牙连接

## 功能说明

### 主要功能

1. **水下探测**
   - 实时显示水下深度数据（最大支持5米深度）
   - 通过颜色渐变展示水体和河床情况
   - 支持缩放和平移操作，查看详细区域

2. **鱼群显示**
   - 显示不同大小鱼群（小型、中型、大型）
   - 标记鱼群所在深度
   - 使用不同颜色区分重要鱼群

3. **数据监控**
   - 实时显示当前水深
   - 水温监测
   - 电池电量显示
   - 设备连接状态监控

4. **参数设置**
   - 灵敏度调节：调整鱼群探测灵敏度
   - 采样间隔设置：调整数据采集频率
   - 深度阈值设置：设置警报触发深度

5. **辅助功能**
   - LED照明控制
   - 屏幕截图
   - 数据记录

### 用户界面

1. **悬浮状态面板**
   - 显示当前深度、水温、电池电量
   - 连接状态指示
   - LED控制开关

2. **全屏水域图**
   - 水体深度渐变显示
   - 河床轮廓热力图
   - 深度刻度标尺
   - 鱼群图标显示

3. **控制面板**
   - 透明悬浮设计
   - 设置滑块和按钮
   - 快捷功能按钮

## 数据流程

1. **数据采集**
   - 硬件设备通过超声波技术测量水深
   - 温度传感器采集水温数据
   - 设备状态监测（电量、连接等）

2. **数据传输**
   - 通过TCP/IP或蓝牙将采集的原始数据传输至应用

3. **数据处理**
   - 应用接收原始数据并进行解析
   - 根据灵敏度设置分析深度变化，识别鱼群
   - 计算并更新历史深度数据

4. **数据展示**
   - 将处理后的数据通过Compose UI组件可视化呈现
   - 动态更新水下地形和鱼群分布

## 适配说明

- **屏幕方向**：支持横屏和竖屏显示
- **全屏模式**：隐藏系统状态栏，提供沉浸式体验
- **不同设备**：自适应不同屏幕尺寸和分辨率

## 注意事项

1. **硬件要求**
   - Android 5.0 (API级别21)或更高版本
   - 支持蓝牙4.0的设备（用于连接鱼探仪硬件）

2. **使用建议**
   - 保持手机充足电量，避免在探测过程中断电
   - 确保鱼探仪设备正确放置在水中
   - 调整适当的灵敏度以获得最佳探测效果
   - 在信号良好的环境下使用

3. **常见问题**
   - 连接问题：检查设备是否开启、电量是否充足
   - 数据异常：尝试重置设备或调整灵敏度
   - 屏幕显示：调整手机亮度以适应室外环境

## 项目结构

```
app/
├── src/
│   ├── main/
│   │   ├── java/com/example/lnnfilsh/
│   │   │   ├── model/           # 数据模型
│   │   │   ├── network/         # 网络通信
│   │   │   ├── ui/
│   │   │   │   ├── components/  # UI组件
│   │   │   │   ├── screens/     # 界面
│   │   │   │   └── theme/       # 主题样式
│   │   │   ├── viewmodel/       # 视图模型
│   │   │   └── MainActivity.kt  # 主活动
│   │   └── res/                 # 资源文件
│   └── test/                    # 测试代码
└── build.gradle.kts             # 构建配置
```

## ESP8266模拟程序

以下是一个用Python编写的模拟ESP8266发送数据的调试程序，可用于测试APP的通信功能：

```python
import socket
import time
import random
import struct
import threading

# 配置参数
HOST = '0.0.0.0'  # 监听所有接口
PORT = 8266       # 默认端口
FRAME_HEADER = b'\xAA\xBB'  # 上行数据帧头
CMD_HEADER = b'\xCC\xDD'    # 下行命令帧头

# 设备状态变量
device_status = {
    'collecting': False,
    'led_status': False,
    'battery': 85,
    'sensitivity': 5,
    'sampling_interval': 50  # 500毫秒
}

# CRC16-MODBUS校验
def calculate_crc16(data):
    crc = 0xFFFF
    for i in range(len(data)):
        crc ^= data[i]
        for _ in range(8):
            if crc & 0x0001:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc = crc >> 1
    return crc.to_bytes(2, byteorder='little')

# 生成深度数据帧
def create_depth_frame():
    # 命令类型：深度数据
    cmd_type = 0x01
    # 数据长度
    data_len = 14
    
    # 随机生成深度数据 (1m - 2.5m)
    depth = int(random.uniform(100, 250))
    # 随机生成水温 (15°C - 25°C)
    temp = int(random.uniform(150, 250))
    
    # 构建数据内容
    data_content = struct.pack('<HH', depth, temp) + bytes(10)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame

# 生成状态数据帧
def create_status_frame():
    # 命令类型：状态数据
    cmd_type = 0x02
    # 数据长度
    data_len = 14
    
    # 构建状态字节
    status_byte = 0
    if device_status['collecting']:
        status_byte |= 0x01
    if device_status['led_status']:
        status_byte |= 0x02
    
    # 构建数据内容
    data_content = bytes([device_status['battery'], status_byte]) + bytes(12)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame

# 生成鱼群数据帧
def create_fish_frame():
    # 命令类型：鱼群数据
    cmd_type = 0x03
    # 数据长度
    data_len = 14
    
    # 随机生成鱼的深度 (0.5m - 3m)
    fish_depth = int(random.uniform(50, 300))
    # 随机生成鱼的大小 (1-小, 2-中, 3-大)
    fish_size = random.randint(1, 3)
    # 随机生成置信度 (60% - 95%)
    confidence = random.randint(60, 95)
    
    # 构建数据内容
    data_content = struct.pack('<HBB', fish_depth, fish_size, confidence) + bytes(10)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame

# 解析命令帧
def parse_command(data):
    # 检查帧头
    if data[0:2] != CMD_HEADER:
        print("无效的命令帧头")
        return
    
    # 提取命令类型
    cmd_type = data[2]
    # 提取参数长度
    param_len = data[3]
    # 提取参数内容
    params = data[4:4+param_len]
    
    # 验证CRC
    received_crc = data[8:10]
    calculated_crc = calculate_crc16(data[2:8])
    if received_crc != calculated_crc:
        print("CRC校验失败")
        return
    
    # 处理不同类型的命令
    if cmd_type == 0x01:  # 开始采集
        device_status['collecting'] = True
        print("收到命令: 开始采集")
        
    elif cmd_type == 0x02:  # 停止采集
        device_status['collecting'] = False
        print("收到命令: 停止采集")
        
    elif cmd_type == 0x03:  # 设置参数
        if param_len >= 2:
            device_status['sensitivity'] = params[0]
            device_status['sampling_interval'] = params[1]
            print(f"收到命令: 设置参数 (灵敏度={params[0]}, 采样间隔={params[1]*10}ms)")
            
    elif cmd_type == 0x04:  # 切换LED
        device_status['led_status'] = not device_status['led_status']
        print(f"收到命令: 切换LED (当前状态={'开' if device_status['led_status'] else '关'})")
        
    elif cmd_type == 0x05:  # 重置设备
        device_status['collecting'] = False
        device_status['led_status'] = False
        device_status['sensitivity'] = 5
        device_status['sampling_interval'] = 50
        print("收到命令: 重置设备")
    
    # 生成响应
    return create_response_frame(cmd_type)

# 生成响应帧
def create_response_frame(original_cmd):
    # 命令类型：响应命令
    cmd_type = 0x04
    # 数据长度
    data_len = 14
    
    # 构建数据内容 (第一个字节是原始命令类型)
    data_content = bytes([original_cmd]) + bytes(13)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame

# 数据发送线程
def data_sender(conn):
    try:
        # 首先发送状态帧确认连接
        conn.sendall(create_status_frame())
        print("已发送初始状态帧")
        
        last_status_time = time.time()
        
        while True:
            # 检查是否在采集状态
            if device_status['collecting']:
                # 发送深度数据帧
                conn.sendall(create_depth_frame())
                print("已发送深度数据帧")
                
                # 随机发送鱼群数据 (20%概率)
                if random.random() < 0.2:
                    conn.sendall(create_fish_frame())
                    print("已发送鱼群数据帧")
            
            # 每5秒发送一次状态帧
            current_time = time.time()
            if current_time - last_status_time >= 5:
                conn.sendall(create_status_frame())
                print("已发送状态数据帧")
                last_status_time = current_time
            
            # 根据采样间隔延时
            time.sleep(device_status['sampling_interval'] * 0.01)
            
    except Exception as e:
        print(f"发送数据时出错: {e}")

# 主函数
def main():
    # 创建TCP服务器
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((HOST, PORT))
    server_socket.listen(1)
    
    print(f"ESP8266模拟器已启动，监听在 {HOST}:{PORT}")
    
    try:
        while True:
            # 等待连接
            conn, addr = server_socket.accept()
            print(f"收到来自 {addr} 的连接")
            
            # 启动数据发送线程
            sender_thread = threading.Thread(target=data_sender, args=(conn,))
            sender_thread.daemon = True
            sender_thread.start()
            
            # 主线程负责接收命令
            try:
                while True:
                    # 接收命令数据
                    data = conn.recv(10)  # 命令帧固定长度为10字节
                    if not data:
                        print("客户端断开连接")
                        break
                    
                    # 解析并处理命令
                    response = parse_command(data)
                    if response:
                        # 发送响应
                        conn.sendall(response)
                        print("已发送响应帧")
            
            except Exception as e:
                print(f"处理客户端数据时出错: {e}")
            
            finally:
                conn.close()
                print("客户端连接已关闭")
    
    except KeyboardInterrupt:
        print("程序被用户中断")
    
    finally:
        server_socket.close()
        print("服务器已关闭")

if __name__ == "__main__":
    main()
```

### 模拟程序使用说明

1. 安装Python 3.6或更高版本
2. 将上述代码保存为`fish_finder_simulator.py`
3. 运行命令：`python fish_finder_simulator.py`
4. 模拟器将在端口8266上监听连接
5. 确保手机与运行模拟器的计算机在同一网络中
6. 在APP中连接到计算机的IP地址和端口8266

该模拟程序实现了完整的通信协议，可用于测试APP的各项功能，包括深度数据显示、鱼群探测、LED控制等。 