-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:38:9-46:20
	android:grantUriPermissions
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:42:13-47
	android:authorities
		INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:40:13-64
	android:exported
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:41:13-37
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:39:13-62
manifest
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:2:1-49:12
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:2:1-49:12
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:2:1-49:12
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:2:1-49:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2721a18d1f384e325377e5ce198a2b2\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a575f2fdfb28ee7d8146806f00191903\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa0e2b3020cb5ff555ca1cfb7c91698\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b11460080e1d7f0e385f7bcc26bd9b3\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf424f4bd841f98dbb7ceca5d9c6cfa1\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fb8bd32822d6bb35cac9f5a3827fbe\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac4c511404cbb5b7e9e3b5cb70751194\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b5a86133d992459d197d64e83d0fba\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b270d593ae2c6fd929e4f14fadb16de\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bc43ef36ef2e03e91db7842da6753a2\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97941244d0e9699216d8c0f799984a00\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac3969aad945845796070ea0de60daa\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f0eeabc09d11f4abafc6614d9cc3392\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e4361f59b5abc9109bcf0935baef32\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7560741b5a7322061b41ba4c34c2636c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70a83252e17e026d3be3252f4db70892\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac5f8210fee3f3cc2c775bd381fe615\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb49844e3e84e139cd8f584c47a02001\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bbba56a43d87abfd487903e5487d9ba\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5a5bbe3148462e0e1db275dd73c508e\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\574fb3b22c97666acee013c7ead937e9\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa47837f1add5634d9cad7ffe5ae3f86\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3b185c6f241dfe0e31b115f8ae8b43c\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d223efed7c9b71e5170a4e85d2ac6918\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ece416787e8579b8ec47dfcdc6a9cbd\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c617eb06af8b9901026f792d3adc3524\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed265b39fbceefd941249b541928e20c\transformed\activity-compose-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05ede6508d5fa2e4c31381d82f0f5c82\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4b01574edba0836818a1a9d62b1b1ef\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1025fdec53f42a3b1baec296861e444c\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c3603112a9d7abc716875471cca5ac\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\909784990c3d45f3f0fdd83263f9e641\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:6:5-76
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:6:22-73
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.VIBRATE
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:8:5-66
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:11:22-77
application
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:14:5-47:19
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:14:5-47:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:19:9-41
	android:fullBackupContent
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:17:9-54
	android:roundIcon
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:23:9-29
	android:icon
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:18:9-43
	android:allowBackup
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:15:9-35
	android:theme
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:22:9-46
	android:dataExtractionRules
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:16:9-65
activity#com.example.lnnfilsh.MainActivity
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:24:9-35:20
	android:screenOrientation
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:28:13-51
	android:exported
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:26:13-36
	android:configChanges
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:27:13-98
	android:theme
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:29:13-50
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:25:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:30:13-34:29
action#android.intent.action.MAIN
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:31:17-69
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:31:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:33:17-77
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:33:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:43:13-45:54
	android:resource
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:45:17-51
	android:name
		ADDED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml:44:17-67
uses-sdk
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2721a18d1f384e325377e5ce198a2b2\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2721a18d1f384e325377e5ce198a2b2\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a575f2fdfb28ee7d8146806f00191903\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a575f2fdfb28ee7d8146806f00191903\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa0e2b3020cb5ff555ca1cfb7c91698\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa0e2b3020cb5ff555ca1cfb7c91698\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b11460080e1d7f0e385f7bcc26bd9b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b11460080e1d7f0e385f7bcc26bd9b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf424f4bd841f98dbb7ceca5d9c6cfa1\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf424f4bd841f98dbb7ceca5d9c6cfa1\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fb8bd32822d6bb35cac9f5a3827fbe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fb8bd32822d6bb35cac9f5a3827fbe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac4c511404cbb5b7e9e3b5cb70751194\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac4c511404cbb5b7e9e3b5cb70751194\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b5a86133d992459d197d64e83d0fba\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b5a86133d992459d197d64e83d0fba\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b270d593ae2c6fd929e4f14fadb16de\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b270d593ae2c6fd929e4f14fadb16de\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bc43ef36ef2e03e91db7842da6753a2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bc43ef36ef2e03e91db7842da6753a2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97941244d0e9699216d8c0f799984a00\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97941244d0e9699216d8c0f799984a00\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac3969aad945845796070ea0de60daa\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac3969aad945845796070ea0de60daa\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f0eeabc09d11f4abafc6614d9cc3392\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f0eeabc09d11f4abafc6614d9cc3392\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e4361f59b5abc9109bcf0935baef32\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e4361f59b5abc9109bcf0935baef32\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7560741b5a7322061b41ba4c34c2636c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7560741b5a7322061b41ba4c34c2636c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70a83252e17e026d3be3252f4db70892\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70a83252e17e026d3be3252f4db70892\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac5f8210fee3f3cc2c775bd381fe615\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac5f8210fee3f3cc2c775bd381fe615\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb49844e3e84e139cd8f584c47a02001\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb49844e3e84e139cd8f584c47a02001\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bbba56a43d87abfd487903e5487d9ba\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bbba56a43d87abfd487903e5487d9ba\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5a5bbe3148462e0e1db275dd73c508e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5a5bbe3148462e0e1db275dd73c508e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\574fb3b22c97666acee013c7ead937e9\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\574fb3b22c97666acee013c7ead937e9\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa47837f1add5634d9cad7ffe5ae3f86\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa47837f1add5634d9cad7ffe5ae3f86\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3b185c6f241dfe0e31b115f8ae8b43c\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3b185c6f241dfe0e31b115f8ae8b43c\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d223efed7c9b71e5170a4e85d2ac6918\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d223efed7c9b71e5170a4e85d2ac6918\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ece416787e8579b8ec47dfcdc6a9cbd\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ece416787e8579b8ec47dfcdc6a9cbd\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c617eb06af8b9901026f792d3adc3524\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c617eb06af8b9901026f792d3adc3524\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed265b39fbceefd941249b541928e20c\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed265b39fbceefd941249b541928e20c\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05ede6508d5fa2e4c31381d82f0f5c82\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05ede6508d5fa2e4c31381d82f0f5c82\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4b01574edba0836818a1a9d62b1b1ef\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4b01574edba0836818a1a9d62b1b1ef\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1025fdec53f42a3b1baec296861e444c\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1025fdec53f42a3b1baec296861e444c\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c3603112a9d7abc716875471cca5ac\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c3603112a9d7abc716875471cca5ac\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\909784990c3d45f3f0fdd83263f9e641\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\909784990c3d45f3f0fdd83263f9e641\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\anAPP\filsh\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.example.lnnfilsh.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.lnnfilsh.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
