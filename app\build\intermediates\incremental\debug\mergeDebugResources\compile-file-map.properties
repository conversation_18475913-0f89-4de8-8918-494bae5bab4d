#Thu May 29 17:43:45 CST 2025
com.example.lnnfilsh.app-main-48\:/drawable/da.png=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_da.png.flat
com.example.lnnfilsh.app-main-48\:/drawable/da_vector.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_da_vector.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_arrow_down.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_down.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_arrow_left.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_left.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_arrow_right.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_right.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_arrow_up.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_up.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_battery.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_battery.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_battery_25.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_battery_25.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_battery_50.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_battery_50.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_battery_75.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_battery_75.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_battery_full.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_battery_full.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_battery_low.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_battery_low.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_bluetooth.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_bluetooth.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_depth.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_depth.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_direction_controls.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_direction_controls.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_fish.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fish.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_launcher_background.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_radar.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_radar.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_screenshot.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_screenshot.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_temperature.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_temperature.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_vibration.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_vibration.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/ic_wifi.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wifi.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/xiao.png=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_xiao.png.flat
com.example.lnnfilsh.app-main-48\:/drawable/xiao_vector.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_xiao_vector.xml.flat
com.example.lnnfilsh.app-main-48\:/drawable/zhong.png=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_zhong.png.flat
com.example.lnnfilsh.app-main-48\:/drawable/zhong_vector.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_zhong_vector.xml.flat
com.example.lnnfilsh.app-main-48\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.lnnfilsh.app-main-48\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.lnnfilsh.app-main-48\:/mipmap-hdpi/ic_launcher.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-mdpi/ic_launcher.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-xhdpi/ic_launcher.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.lnnfilsh.app-main-48\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.lnnfilsh.app-main-48\:/raw/beep.MP3=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\raw_beep.MP3.flat
com.example.lnnfilsh.app-main-48\:/xml/backup_rules.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.lnnfilsh.app-main-48\:/xml/data_extraction_rules.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.lnnfilsh.app-main-48\:/xml/file_paths.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.example.lnnfilsh.app-pngs-42\:/drawable-anydpi-v24/ic_launcher_foreground.xml=D\:\\anAPP\\filsh\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat
