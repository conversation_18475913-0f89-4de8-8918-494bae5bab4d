package com.example.lnnfilsh.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lnnfilsh.R
import com.example.lnnfilsh.ui.theme.AccentBlue
import com.example.lnnfilsh.ui.theme.AccentGreen
import com.example.lnnfilsh.ui.theme.ControlButtonBg
import com.example.lnnfilsh.ui.theme.ControlButtonTextColor
import com.example.lnnfilsh.ui.theme.ControlPanelBackground
import com.example.lnnfilsh.ui.theme.DisconnectedRed

@Composable
fun BottomControlBar(
    isCollecting: Boolean,
    onStartStopClick: () -> Unit,
    isConnected: Boolean,
    onConnectClick: () -> Unit,
    onScreenshotClick: () -> Unit,
    onResetClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp
    val screenHeight = configuration.screenHeightDp
    val isLandscape = screenWidth > screenHeight
    
    val fontSize = if (isLandscape) 8.sp else if (screenWidth < 360) 10.sp else 12.sp
    val buttonSize = if (isLandscape) 20.dp else if (screenWidth < 360) 25.dp else 30.dp
    val buttonHorizontalPadding = if (isLandscape) 2.dp else 8.dp
    val buttonCornerRadius = if (isLandscape) 4.dp else 8.dp
    
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = ControlPanelBackground
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = 12.dp,
                    vertical = if (isLandscape) 2.dp else 8.dp
                ),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 启动/停止按钮
            Button(
                onClick = onStartStopClick,
                modifier = Modifier.height(buttonSize),
                shape = RoundedCornerShape(buttonCornerRadius),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isCollecting) DisconnectedRed else AccentGreen,
                    contentColor = ControlButtonTextColor
                )
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = if (isCollecting) Icons.Default.Stop else Icons.Default.PlayArrow,
                        contentDescription = if (isCollecting) "停止" else "开始",
                        modifier = Modifier.size(buttonSize)
                    )
                    if (!isLandscape) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = if (isCollecting) "停止" else "开始",
                            fontWeight = FontWeight.Bold,
                            fontSize = fontSize
                        )
                    }
                }
            }
            
            // 连接按钮
            Button(
                onClick = onConnectClick,
                modifier = Modifier.height(buttonSize),
                shape = RoundedCornerShape(buttonCornerRadius),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isConnected) AccentBlue else ControlButtonBg,
                    contentColor = ControlButtonTextColor
                )
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    if (isConnected) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_wifi),
                            contentDescription = "断开",
                            modifier = Modifier.size(buttonSize)
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "连接",
                            modifier = Modifier.size(buttonSize)
                        )
                    }
                    if (!isLandscape) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = if (isConnected) "断开" else "连接",
                            fontWeight = FontWeight.Bold,
                            fontSize = fontSize
                        )
                    }
                }
            }
            
            // 截图按钮
            IconButton(
                onClick = onScreenshotClick,
                modifier = Modifier
                    .size(buttonSize)
                    .clip(CircleShape)
                    .background(ControlButtonBg)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_screenshot),
                    contentDescription = "截图",
                    tint = ControlButtonTextColor,
                    modifier = Modifier.size(buttonSize * 0.7f)
                )
            }
            
            // 重置按钮
            IconButton(
                onClick = onResetClick,
                modifier = Modifier
                    .size(buttonSize)
                    .clip(CircleShape)
                    .background(ControlButtonBg)
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "重置",
                    tint = ControlButtonTextColor,
                    modifier = Modifier.size(buttonSize * 0.7f)
                )
            }
        }
    }
} 