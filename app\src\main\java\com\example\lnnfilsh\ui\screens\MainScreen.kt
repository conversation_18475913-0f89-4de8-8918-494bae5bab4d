package com.example.lnnfilsh.ui.screens

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.ArrowLeft
import androidx.compose.material.icons.filled.ArrowRight
import androidx.compose.material.icons.filled.Lightbulb
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.WifiOff
import androidx.compose.material.icons.outlined.Lightbulb
import androidx.compose.material.icons.outlined.Wifi
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.lnnfilsh.R
import com.example.lnnfilsh.ui.components.BottomControlBar
import com.example.lnnfilsh.ui.components.ControlPanel
import com.example.lnnfilsh.ui.components.DepthChartWithLabels
import com.example.lnnfilsh.ui.components.FloatingStatusPanel
import com.example.lnnfilsh.ui.theme.AccentBlue
import com.example.lnnfilsh.ui.theme.WaterDeepBlue
import com.example.lnnfilsh.viewmodel.FishDetectorViewModel
import com.example.lnnfilsh.viewmodel.ViewModelFactory
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.layout.offset
import android.app.Application
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import android.media.MediaPlayer
import android.util.Log
import android.os.Handler
import android.os.Looper
import com.example.lnnfilsh.utils.ScreenshotUtil

@Composable
fun MainScreen(
    viewModel: FishDetectorViewModel = viewModel(
        factory = ViewModelFactory(LocalContext.current.applicationContext as Application)
    )
) {
    // 收集状态
    val currentDepth by viewModel.currentDepth.collectAsState()
    val waterTemperature by viewModel.waterTemperature.collectAsState()
    val batteryPercentage by viewModel.batteryPercentage.collectAsState()
    val isConnected by viewModel.connectionStatus.collectAsState()
    val isCollecting by viewModel.isCollecting.collectAsState()
    val sensitivity by viewModel.sensitivity.collectAsState()
    val samplingInterval by viewModel.samplingInterval.collectAsState()
    val depthHistory by viewModel.depthHistory.collectAsState()
    val fishData by viewModel.fishData.collectAsState()
    val ledStatus by viewModel.ledStatus.collectAsState()
    val ultrasonicData by viewModel.ultrasonicData.collectAsState()
    val vibrationEnabled by viewModel.vibrationEnabled.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    
    // 方向控制状态
    val isMovingForward by viewModel.isMovingForward.collectAsState()
    val isMovingBackward by viewModel.isMovingBackward.collectAsState()
    val isTurningLeft by viewModel.isTurningLeft.collectAsState()
    val isTurningRight by viewModel.isTurningRight.collectAsState()
    val showDirectionButtons by viewModel.showDirectionButtons.collectAsState()
    val displayStyle by viewModel.displayStyle.collectAsState()

    // 设置面板显示状态
    var showSettingsPanel by remember { mutableStateOf(false) }
    
    // 设置选项卡状态
    var selectedTabIndex by remember { mutableStateOf(0) }
    
    val context = LocalContext.current
    val configuration = LocalConfiguration.current
    val layoutDirection = LocalLayoutDirection.current
    val screenHeight = configuration.screenHeightDp
    val screenWidth = configuration.screenWidthDp
    val isLandscape = screenWidth > screenHeight
    
    // 获取系统状态栏和导航栏尺寸
    val statusBarsPadding = WindowInsets.statusBars.asPaddingValues()
    val navigationBarsPadding = WindowInsets.navigationBars.asPaddingValues()
    
    // 新增：当errorMessage不为null时，显示Toast提示
    LaunchedEffect(errorMessage) {
        errorMessage?.let {
            try {
                Toast.makeText(context, it, Toast.LENGTH_LONG).show()
                viewModel.clearErrorMessage() // 清除错误信息，避免重复显示
            } catch (e: Exception) {
                Log.e("MainScreen", "显示错误提示时出错: ${e.message}")
            }
        }
    }
    
    // 添加全局异常捕获
    LaunchedEffect(Unit) {
        try {
            val defaultExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
            Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
                try {
                    Log.e("MainScreen", "捕获到未处理异常: ${throwable.message}", throwable)
                    val errorMsg = "应用发生错误: ${throwable.message?.take(50) ?: "未知错误"}"
                    viewModel.updateErrorMessage(errorMsg)
                    
                    // 延迟一秒后再调用原始处理程序，给Toast显示的时间
                    Handler(Looper.getMainLooper()).postDelayed({
                        defaultExceptionHandler?.uncaughtException(thread, throwable)
                    }, 1000)
                } catch (e: Exception) {
                    Log.e("MainScreen", "处理未捕获异常时出错", e)
                    defaultExceptionHandler?.uncaughtException(thread, throwable)
                }
            }
        } catch (e: Exception) {
            Log.e("MainScreen", "设置全局异常处理器时出错: ${e.message}")
        }
    }
    
    // 修改声音提示逻辑，当收到鱼群数据时直接提示，无需比较数量变化
    LaunchedEffect(fishData) {
        // 每当接收到新的鱼群数据并且满足条件时，就播放声音
        if (fishData.isNotEmpty() && isCollecting && vibrationEnabled) {
            // 只有在采集模式和声音开关打开的情况下触发
            playBeepSound(context)
        }
    }
    
    // 创建UI - 全屏水域显示，控制按钮直接叠加
    Surface(
        color = WaterDeepBlue,
        modifier = Modifier.fillMaxSize()
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    top = 0.dp,
                    bottom = navigationBarsPadding.calculateBottomPadding() + 8.dp,
                    start = 8.dp,
                    end = 8.dp
                )
        ) {
            // 主内容区域 - 带圆角的卡片
            Card(
                modifier = Modifier.fillMaxSize(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = WaterDeepBlue
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 深度图表 - 全屏背景
                    DepthChartWithLabels(
                        depthHistory = depthHistory,
                        fishData = fishData,
                        currentDepth = currentDepth,
                        waterTemperature = waterTemperature,
                        batteryPercentage = batteryPercentage,
                        isConnected = isConnected,
                        ultrasonicData = ultrasonicData,
                        displayStyle = displayStyle, // 传递显示样式
                        onConnectToggle = {
                            // 点击时切换连接状态
                            if (isConnected) viewModel.disconnect() else viewModel.connect()
                        },
                        modifier = Modifier.fillMaxSize()
                    )
                    
                    // 设置按钮 - 根据屏幕方向调整位置
                    FloatingActionButton(
                        onClick = {
                            showSettingsPanel = !showSettingsPanel
                        },
                        containerColor = if (showSettingsPanel) AccentBlue else Color.DarkGray.copy(alpha = 0.1f),
                        contentColor = Color.White,
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(
                                end = if (isLandscape) 50.dp else 16.dp,
                                bottom = if (isLandscape) 10.dp else 16.dp
                            )
                            .size(48.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Settings,
                            contentDescription = "设置"
                        )
                    }
                    
                    // 设置面板 - 右侧，只在点击设置按钮时显示
                    if (showSettingsPanel) {
                        // 添加全屏透明层用于点击关闭
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(Color.Black.copy(alpha = 0.3f))
                                .clickable { showSettingsPanel = false }
                        ) {
                            // 设置面板卡片
                            Card(
                                modifier = Modifier
                                    .align(if (isLandscape) Alignment.Center else Alignment.TopEnd)
                                    .padding(
                                        top = if (isLandscape) 0.dp else 80.dp,
                                        end = if (isLandscape) 0.dp else 16.dp,
                                        start = if (isLandscape) 0.dp else 16.dp
                                    )
                                    .width(250.dp)
                                    .alpha(0.95f)
                                    // 防止点击卡片时触发背景点击
                                    .clickable { /* 拦截点击事件，防止关闭窗口 */ }
                                    .pointerInput(Unit) {
                                        detectVerticalDragGestures { _, dragAmount ->
                                            if (dragAmount > 10) { // 向下滑动超过阈值时关闭
                                                showSettingsPanel = false
                                            }
                                        }
                                    },
                                shape = RoundedCornerShape(16.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = WaterDeepBlue.copy(alpha = 0.95f)
                                ),
                                elevation = CardDefaults.cardElevation(
                                    defaultElevation = 8.dp
                                )
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp)
                                        .verticalScroll(rememberScrollState()),
                                    verticalArrangement = Arrangement.spacedBy(12.dp)
                                ) {
                                    // 添加顶部拖动条，用于视觉提示可以滑动关闭
                                    Box(
                                        modifier = Modifier
                                            .align(Alignment.CenterHorizontally)
                                            .width(60.dp)
                                            .height(4.dp)
                                            .clip(RoundedCornerShape(2.dp))
                                            .background(Color.White.copy(alpha = 0.5f))
                                            .padding(bottom = 12.dp)
                                    )
                                    
                                    // 选项卡
                                    TabRow(
                                        selectedTabIndex = selectedTabIndex,
                                        containerColor = Color.Transparent,
                                        contentColor = Color.White,
                                        indicator = { tabPositions ->
                                            TabRowDefaults.SecondaryIndicator(
                                                modifier = Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex]),
                                                height = 2.dp,
                                                color = AccentBlue
                                            )
                                        }
                                    ) {
                                        Tab(
                                            selected = selectedTabIndex == 0,
                                            onClick = { selectedTabIndex = 0 },
                                            text = { Text("基础设置") }
                                        )
                                        Tab(
                                            selected = selectedTabIndex == 1,
                                            onClick = { selectedTabIndex = 1 },
                                            text = { Text("高级设置") }
                                        )
                                    }
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    // 基础设置
                                    if (selectedTabIndex == 0) {
                                        // WiFi连接开关
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                if (isConnected) {
                                                    Icon(
                                                        painter = painterResource(id = R.drawable.ic_wifi),
                                                        contentDescription = "已连接",
                                                        tint = Color.Green,
                                                        modifier = Modifier.size(24.dp)
                                                    )
                                                } else {
                                                    Icon(
                                                        imageVector = Icons.Filled.WifiOff,
                                                        contentDescription = "未连接",
                                                        tint = Color.Red,
                                                        modifier = Modifier.size(24.dp)
                                                    )
                                                }
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(
                                                    text = if (isConnected) "已连接" else "未连接",
                                                    color = Color.White
                                                )
                                            }
                                            Switch(
                                                checked = isConnected,
                                                onCheckedChange = {
                                                    if (isConnected) viewModel.disconnect() else viewModel.connect()
                                                },
                                                colors = SwitchDefaults.colors(
                                                    checkedThumbColor = Color.Green,
                                                    checkedTrackColor = Color.Green.copy(alpha = 0.5f),
                                                    uncheckedThumbColor = Color.Red,
                                                    uncheckedTrackColor = Color.Red.copy(alpha = 0.5f)
                                                )
                                            )
                                        }
                                        
                                        HorizontalDivider(color = Color.White.copy(alpha = 0.3f))
                                        
                                        // LED灯开关
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Icon(
                                                    imageVector = if (ledStatus) Icons.Filled.Lightbulb else Icons.Outlined.Lightbulb,
                                                    contentDescription = if (ledStatus) "LED开启" else "LED关闭",
                                                    tint = if (ledStatus) Color.Yellow else Color.White,
                                                    modifier = Modifier.size(24.dp)
                                                )
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(
                                                    text = if (ledStatus) "LED开启" else "LED关闭",
                                                    color = Color.White
                                                )
                                            }
                                            Switch(
                                                checked = ledStatus,
                                                onCheckedChange = {
                                                    viewModel.toggleLed()
                                                },
                                                colors = SwitchDefaults.colors(
                                                    checkedThumbColor = Color.Yellow,
                                                    checkedTrackColor = Color.Yellow.copy(alpha = 0.5f)
                                                )
                                            )
                                        }
                                        
                                        HorizontalDivider(color = Color.White.copy(alpha = 0.3f))

                                        // 显示样式切换
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Icon(
                                                    painter = painterResource(id = if (displayStyle == 0) R.drawable.ic_fish else R.drawable.ic_radar),
                                                    contentDescription = "显示样式",
                                                    tint = Color.White,
                                                    modifier = Modifier.size(24.dp)
                                                )
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(
                                                    text = if (displayStyle == 0) "真实水底" else "声呐模式",
                                                    color = Color.White
                                                )
                                            }
                                            Switch(
                                                checked = displayStyle == 1,
                                                onCheckedChange = { isChecked ->
                                                    viewModel.updateDisplayStyle(if (isChecked) 1 else 0)
                                                },
                                                colors = SwitchDefaults.colors(
                                                    checkedThumbColor = Color(0xFF00FF88),
                                                    checkedTrackColor = Color(0xFF00FF88).copy(alpha = 0.5f),
                                                    uncheckedThumbColor = AccentBlue,
                                                    uncheckedTrackColor = AccentBlue.copy(alpha = 0.5f)
                                                )
                                            )
                                        }

                                        HorizontalDivider(color = Color.White.copy(alpha = 0.3f))

                                        // 振动开关
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Icon(
                                                    painter = painterResource(id = R.drawable.ic_vibration),
                                                    contentDescription = "声音提醒",
                                                    tint = if (vibrationEnabled) Color.White else Color.Gray,
                                                    modifier = Modifier.size(24.dp)
                                                )
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(
                                                    text = "声音提醒",
                                                    color = Color.White
                                                )
                                            }
                                            Switch(
                                                checked = vibrationEnabled,
                                                onCheckedChange = {
                                                    viewModel.updateVibrationEnabled(it)
                                                },
                                                colors = SwitchDefaults.colors(
                                                    checkedThumbColor = AccentBlue,
                                                    checkedTrackColor = AccentBlue.copy(alpha = 0.5f)
                                                )
                                            )
                                        }
                                        
                                        HorizontalDivider(color = Color.White.copy(alpha = 0.3f))
                                        
                                        // 重置设备按钮
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Filled.Refresh,
                                                    contentDescription = "重置设备",
                                                    tint = Color.White,
                                                    modifier = Modifier.size(24.dp)
                                                )
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(
                                                    text = "重置设备",
                                                    color = Color.White
                                                )
                                            }
                                            IconButton(
                                                onClick = {
                                                    viewModel.resetDevice()
                                                    Toast.makeText(context, "设备已重置", Toast.LENGTH_SHORT).show()
                                                }
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Filled.Refresh,
                                                    contentDescription = "重置",
                                                    tint = Color.White
                                                )
                                            }
                                        }
                                        
                                        HorizontalDivider(color = Color.White.copy(alpha = 0.3f))
                                        
                                        // 分享功能
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Row(
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Icon(
                                                    painter = painterResource(R.drawable.ic_screenshot),
                                                    contentDescription = "截图分享",
                                                    tint = Color.White,
                                                    modifier = Modifier.size(24.dp)
                                                )
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(
                                                    text = "截图分享",
                                                    color = Color.White
                                                )
                                            }
                                            IconButton(
                                                onClick = {
                                                    try {
                                                        // 使用ScreenshotUtil截图并分享
                                                        val rootView = (context as? android.app.Activity)?.window?.decorView
                                                        rootView?.let { view ->
                                                            // 先关闭设置面板，再截图
                                                            showSettingsPanel = false
                                                            // 延迟截图，确保面板已关闭
                                                            Handler(Looper.getMainLooper()).postDelayed({
                                                                ScreenshotUtil.takeScreenshotAndShare(context, view)
                                                            }, 200)
                                                        }
                                                    } catch (e: Exception) {
                                                        Toast.makeText(context, "截图分享失败: ${e.message}", Toast.LENGTH_SHORT).show()
                                                    }
                                                }
                                            ) {
                                                Icon(
                                                    painter = painterResource(R.drawable.ic_screenshot),
                                                    contentDescription = "截图",
                                                    tint = Color.White
                                                )
                                            }
                                        }
                                        
                                        // 仅在横屏模式下显示方向按钮控制选项
                                        if (isLandscape) {
                                            HorizontalDivider(color = Color.White.copy(alpha = 0.3f))
                                            
                                            // 方向按钮开关
                                            Row(
                                                modifier = Modifier.fillMaxWidth(),
                                                verticalAlignment = Alignment.CenterVertically,
                                                horizontalArrangement = Arrangement.SpaceBetween
                                            ) {
                                                Row(
                                                    verticalAlignment = Alignment.CenterVertically
                                                ) {
                                                    Icon(
                                                        painter = painterResource(id = R.drawable.ic_direction_controls),
                                                        contentDescription = "方向按钮",
                                                        tint = Color.White,
                                                        modifier = Modifier.size(24.dp)
                                                    )
                                                    Spacer(modifier = Modifier.width(8.dp))
                                                    Text(
                                                        text = "显示方向控制",
                                                        color = Color.White
                                                    )
                                                }
                                                Switch(
                                                    checked = showDirectionButtons,
                                                    onCheckedChange = { viewModel.updateDirectionButtonsVisibility(it) },
                                                    colors = SwitchDefaults.colors(
                                                        checkedThumbColor = AccentBlue,
                                                        checkedTrackColor = AccentBlue.copy(alpha = 0.5f),
                                                        uncheckedThumbColor = Color.Gray,
                                                        uncheckedTrackColor = Color.Gray.copy(alpha = 0.5f)
                                                    )
                                                )
                                            }
                                        }
                                    } 
                                    // 高级设置
                                    else if (selectedTabIndex == 1) {
                                        // 灵敏度滑块
                                        Text(
                                            text = "灵敏度: ${sensitivity.toInt()}",
                                            color = Color.White
                                        )
                                        Slider(
                                            value = sensitivity,
                                            onValueChange = { viewModel.updateSensitivity(it) },
                                            valueRange = 1f..10f,
                                            steps = 9,
                                            colors = SliderDefaults.colors(
                                                thumbColor = AccentBlue,
                                                activeTrackColor = AccentBlue,
                                                inactiveTrackColor = Color.Gray
                                            ),
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                        
                                        // 采样间隔滑块
                                        Text(
                                            text = "采样间隔: ${samplingInterval}ms",
                                            color = Color.White
                                        )
                                        Slider(
                                            value = samplingInterval.toFloat(),
                                            onValueChange = { viewModel.updateSamplingInterval(it.toInt()) },
                                            valueRange = 10f..100f,
                                            steps = 9,
                                            colors = SliderDefaults.colors(
                                                thumbColor = AccentBlue,
                                                activeTrackColor = AccentBlue,
                                                inactiveTrackColor = Color.Gray
                                            ),
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    // 横屏模式下的方向控制按钮
                    if (isLandscape && showDirectionButtons) {
                        // 前进按钮 - 左侧中上方
                        FloatingActionButton(
                            onClick = { 
                                viewModel.moveForward(true)
                            },
                            containerColor = Color.Gray.copy(alpha = 0.1f),
                            contentColor = Color.White,
                            modifier = Modifier
                                .offset(x = 80.dp, y = (-10).dp)
                                .align(Alignment.CenterStart)
                                .size(56.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_arrow_up),
                                contentDescription = "前进",
                                modifier = Modifier.size(32.dp)
                            )
                        }
                        
                        // 后退按钮 - 左侧中下方
                        FloatingActionButton(
                            onClick = { 
                                viewModel.moveBackward(true)
                            },
                            containerColor = Color.Gray.copy(alpha = 0.1f),
                            contentColor = Color.White,
                            modifier = Modifier
                                .offset(x = 80.dp, y = 100.dp)
                                .align(Alignment.CenterStart)
                                .size(56.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_arrow_down),
                                contentDescription = "后退",
                                modifier = Modifier.size(32.dp)
                            )
                        }
                        
                        // 左转按钮 - 左侧中间靠左
                        FloatingActionButton(
                            onClick = { 
                                viewModel.turnLeft(true)
                            },
                            containerColor = Color.Gray.copy(alpha = 0.0f),
                            contentColor = Color.White,
                            modifier = Modifier
                                .offset(x = 650.dp, y = 40.dp)
                                .align(Alignment.CenterStart)
                                .size(56.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_arrow_left),
                                contentDescription = "左转",
                                modifier = Modifier.size(32.dp)
                            )
                        }
                        
                        // 右转按钮 - 左侧中间靠右
                        FloatingActionButton(
                            onClick = { 
                                viewModel.turnRight(true)
                            },
                            containerColor = Color.Gray.copy(alpha = 0.0f),
                            contentColor = Color.White,
                            modifier = Modifier
                                .offset(x = 750.dp, y = 40.dp)
                                .align(Alignment.CenterStart)
                                .size(56.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_arrow_right),
                                contentDescription = "右转",
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 应用启动时自动尝试连接
    LaunchedEffect(Unit) {
        viewModel.connect()
    }
    
    // 组件销毁时断开连接
    DisposableEffect(Unit) {
        onDispose {
            viewModel.disconnect()
        }
    }
    
    // 监听方向控制按钮释放
    LaunchedEffect(isMovingForward, isMovingBackward, isTurningLeft, isTurningRight) {
        // 当任何方向按钮被激活时，添加停止逻辑
        if (isMovingForward || isMovingBackward || isTurningLeft || isTurningRight) {
            // 这里可以添加一些额外的UI反馈
        }
    }
}

// 播放提示音
private fun playBeepSound(context: Context) {
    try {
        val mediaPlayer = MediaPlayer.create(context, R.raw.beep)
        mediaPlayer.setOnCompletionListener { mp ->
            mp.release()
        }
        mediaPlayer.start()
    } catch (e: Exception) {
        Log.e("MainScreen", "播放提示音失败: ${e.message}")
    }
} 