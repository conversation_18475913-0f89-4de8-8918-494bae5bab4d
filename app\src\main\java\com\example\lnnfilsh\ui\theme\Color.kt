package com.example.lnnfilsh.ui.theme

import androidx.compose.ui.graphics.Color

// 基础主题颜色
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)
val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// 水域主题颜色
val WaterLightBlue = Color(0xFF64B5F6)
val WaterMediumBlue = Color(0xFF1976D2)
val WaterDeepBlue = Color(0xFF0D47A1)
val WaterBlue = Color(0xFF1E88E5)
val DarkBlue = Color(0xFF0A1929)

// 强调色
val HighlightColor = Color(0xFF00B8D4)
val AccentBlue = Color(0xFF2196F3)
val AccentGreen = Color(0xFF26A69A)
val BrightAccent = Color(0xFF00E5FF)
val DisconnectedRed = Color(0xFFE53935)

// 控制面板颜色
val ControlPanelBackground = Color(0xFF143875).copy(alpha = 0.9f)
val ControlButtonBg = Color(0xFF37474F)
val ControlButtonTextColor = Color.White

// 鱼图标颜色
val FishIconYellow = Color(0xFFFFEB3B)
val FishIconWhite = Color(0xFFE0E0E0)
val FishIconColor = Color(0xFFFFEA00)
val FishIconOutlineColor = Color(0xFF000000)

// 鱼类大小颜色
val SmallFishColor = Color(0xFFFFE082)
val MediumFishColor = Color(0xFFFFC107)
val LargeFishColor = Color(0xFFFF9800)

// 河床热力图颜色
val RiverbedYellow = Color(0xFFFFD54F)
val RiverbedOrange = Color(0xFFFF9800)
val RiverbedRed = Color(0xFFE53935)
val RiverbedGreen = Color(0xFF43A047)
val RiverbedColor = Color(0xFF795548)

// 增强的河床颜色 - 更丰富的层次
val RiverbedSurfaceGold = Color(0xFFFFE082)
val RiverbedMidOrange = Color(0xFFFFB74D)
val RiverbedDeepOrange = Color(0xFFFF8A65)
val RiverbedDarkOrange = Color(0xFFD84315)
val RiverbedSoilBrown = Color(0xFF8D6E63)

// 水体增强颜色
val WaterSkyBlue = Color(0xFF87CEEB)
val WaterSteelBlue = Color(0xFF4682B4)
val WaterDodgerBlue = Color(0xFF1E90FF)
val WaterDeepNavy = Color(0xFF0066CC)
val WaterAbyssBlue = Color(0xFF003366)

// 图表颜色
val ChartLineColor = Color(0xFF29B6F6)
val DepthScaleColor = Color.White.copy(alpha = 0.8f)
val WaterLayerStartColor = Color(0xFF0288D1).copy(alpha = 0.4f)
val WaterLayerEndColor = Color(0xFF01579B).copy(alpha = 0.6f)

// 背景颜色
val SurfaceLight = Color(0xFFF5F5F5)
val SurfaceDark = Color(0xFF121212)
val DarkTransparentBackground = Color(0x99000000)
val LightTransparentBackground = Color.White.copy(alpha = 0.1f)

// 功能色
val ConnectedGreen = Color(0xFF4CAF50)