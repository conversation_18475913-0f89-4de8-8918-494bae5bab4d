package com.example.lnnfilsh.ui.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lnnfilsh.R
import com.example.lnnfilsh.model.FishData
import com.example.lnnfilsh.model.FishSize
import com.example.lnnfilsh.ui.theme.AccentBlue
import com.example.lnnfilsh.ui.theme.DarkTransparentBackground
import com.example.lnnfilsh.ui.theme.FishIconWhite
import com.example.lnnfilsh.ui.theme.FishIconYellow
import com.example.lnnfilsh.ui.theme.RiverbedGreen
import com.example.lnnfilsh.ui.theme.RiverbedOrange
import com.example.lnnfilsh.ui.theme.RiverbedRed
import com.example.lnnfilsh.ui.theme.RiverbedYellow
import com.example.lnnfilsh.ui.theme.RiverbedSurfaceGold
import com.example.lnnfilsh.ui.theme.RiverbedMidOrange
import com.example.lnnfilsh.ui.theme.RiverbedDeepOrange
import com.example.lnnfilsh.ui.theme.RiverbedDarkOrange
import com.example.lnnfilsh.ui.theme.RiverbedSoilBrown
import com.example.lnnfilsh.ui.theme.WaterSkyBlue
import com.example.lnnfilsh.ui.theme.WaterSteelBlue
import com.example.lnnfilsh.ui.theme.WaterDodgerBlue
import com.example.lnnfilsh.ui.theme.WaterDeepNavy
import com.example.lnnfilsh.ui.theme.WaterAbyssBlue
import com.example.lnnfilsh.ui.theme.WaterDeepBlue
import com.example.lnnfilsh.ui.theme.WaterLightBlue
import com.example.lnnfilsh.ui.theme.WaterMediumBlue
import kotlin.math.min

@Composable
fun DepthChart(
    depthHistory: List<Float>,
    fishData: List<FishData>,
    ultrasonicData: List<Float> = emptyList(), // 添加超声波数据参数
    modifier: Modifier = Modifier
) {
    // 移除缩放状态变量
    val textMeasurer = rememberTextMeasurer()
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    
    // 获取屏幕尺寸
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }
    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }
    
    // 根据当前水深数据自动选择显示范围
    val currentMaxDepth = remember(depthHistory) {
        if (depthHistory.isEmpty()) {
            100f // 无数据时使用默认值1.5米，避免除零错误
        } else {
            val maxValue = depthHistory.maxOrNull() ?: 100f
            // 根据最大深度选择合适的标尺范围，以0.5米为基准
            when {
                maxValue <= 100f -> 100f // 1米
                maxValue <= 150f -> 150f // 1.5米
                maxValue <= 200f -> 200f // 3米
                maxValue <= 300f -> 300f // 3米
                maxValue <= 450f -> 450f // 4.5米
                maxValue <= 500f -> 500f // 5米
                else -> 600f // 6米，但会显示超出范围提示
            }
        }
    }
    
    // 固定水深标尺以0.5米为基准
    val depthLabelCount = (currentMaxDepth / 50f).toInt() // 每50厘米(0.5米)一个刻度
    val depthInterval = 50f // 0.5米间隔
    
    // 水底渐变高度百分比 - 调整红框区域大小
    val riverBedHeightPercentage = 0.4f // 调整河床部分占比，确保河床正确显示
    
    // 顶部边距百分比，用于避免被状态栏遮挡
    val topMarginPercentage = 0.15f // 减小顶部边距，留出更多空间显示深度标尺
    
    // 是否超出5米探测范围
    val isExceedingRange = depthHistory.any { it > 500f }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // 水深图表
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clipToBounds()
        ) {
            val width = size.width
            val height = size.height
            
            // 考虑顶部边距后的实际可绘制高度
            val drawableHeight = height * (1f - topMarginPercentage)
            val topMargin = height * topMarginPercentage
            
            // 绘制增强的水体渐变背景
            val waterGradient = Brush.verticalGradient(
                colors = listOf(
                    WaterSkyBlue.copy(alpha = 0.7f),    // 天空蓝 - 水面
                    WaterSteelBlue.copy(alpha = 0.8f),  // 钢蓝色 - 浅水层
                    WaterDodgerBlue.copy(alpha = 0.85f), // 道奇蓝 - 中水层
                    WaterDeepNavy.copy(alpha = 0.9f),   // 深蓝色 - 深水层
                    WaterAbyssBlue.copy(alpha = 0.95f)  // 深海蓝 - 底层
                )
            )
            drawRect(brush = waterGradient, size = Size(width, height))

            // 添加水波纹效果
            val waveGradient = Brush.radialGradient(
                colors = listOf(
                    WaterSkyBlue.copy(alpha = 0.3f),
                    Color.Transparent,
                    WaterSteelBlue.copy(alpha = 0.2f),
                    Color.Transparent
                ),
                radius = width * 0.8f,
                center = Offset(width * 0.3f, topMargin + drawableHeight * 0.2f)
            )
            drawRect(brush = waveGradient, size = Size(width, height))

            // 添加第二层水波纹
            val waveGradient2 = Brush.radialGradient(
                colors = listOf(
                    WaterDodgerBlue.copy(alpha = 0.25f),
                    Color.Transparent,
                    WaterDeepNavy.copy(alpha = 0.15f),
                    Color.Transparent
                ),
                radius = width * 0.6f,
                center = Offset(width * 0.7f, topMargin + drawableHeight * 0.4f)
            )
            drawRect(brush = waveGradient2, size = Size(width, height))
            
            // 绘制垂直网格线
            val gridCols = 5
            for (i in 1 until gridCols) {
                val x = (width * i) / gridCols
                drawLine(
                    color = Color.White.copy(alpha = 0.2f),
                    start = Offset(x, topMargin),
                    end = Offset(x, height),
                    strokeWidth = 1f
                )
            }
            
            // 绘制超声波探测数据 - 考虑顶部边距
            if (ultrasonicData.isNotEmpty() && currentMaxDepth > 0f) {
                try {
                    // 计算每个点的X坐标间隔
                    val pointInterval = width / (ultrasonicData.size + 1)
                    
                    // 创建超声波数据路径
                    val ultrasonicPath = Path()
                    val firstX = pointInterval
                    // 确保第一个点有效
                    val firstPoint = ultrasonicData.first().coerceIn(0f, currentMaxDepth)
                    val firstY = topMargin + (firstPoint / currentMaxDepth) * drawableHeight
                    ultrasonicPath.moveTo(firstX, firstY)
                    
                    // 连接所有点
                    ultrasonicData.forEachIndexed { index, depth ->
                        if (index > 0) {
                            val x = pointInterval * (index + 1)
                            // 确保深度值在合理范围内
                            val validDepth = depth.coerceIn(0f, currentMaxDepth)
                            val y = topMargin + (validDepth / currentMaxDepth) * drawableHeight
                            ultrasonicPath.lineTo(x, y)
                        }
                    }
                    
                    // 绘制超声波轮廓线
                    drawPath(
                        path = ultrasonicPath,
                        color = Color.Cyan.copy(alpha = 0.9f),
                        style = Stroke(
                            width = 2f,
                            pathEffect = PathEffect.dashPathEffect(floatArrayOf(8f, 4f), 0f)
                        )
                    )
                    
                    // 绘制每个数据点
                    ultrasonicData.forEachIndexed { index, depth ->
                        try {
                            val x = pointInterval * (index + 1)
                            // 确保深度值在合理范围内
                            val validDepth = depth.coerceIn(0f, currentMaxDepth)
                            val y = topMargin + (validDepth / currentMaxDepth) * drawableHeight
                            
                            // 绘制点
                            drawCircle(
                                color = Color.Cyan,
                                radius = 4f,
                                center = Offset(x, y)
                            )
                            
                            // 显示深度值 - 调整位置，显示在点的上方
                            val depthText = String.format("%.1f", validDepth / 100)
                            val textStyle = TextStyle(
                                fontSize = 12.sp, // 稍微加大字体
                                color = Color.White,
                                fontWeight = FontWeight.Bold,
                                background = Color.Black.copy(alpha = 0.3f),
                                textAlign = TextAlign.Center
                            )
                            val textSize = textMeasurer.measure(depthText, textStyle)
                            
                            // 计算文本绘制的起始X坐标，确保在有效范围内
                            var textX = x - textSize.size.width / 2
                            // 确保文本的左边缘不会超出画布的左边界(0f)
                            if (textX < 0f) {
                                textX = 0f
                            }
                            // 确保文本的右边缘不会超出画布的右边界
                            if (textX + textSize.size.width > width) {
                                textX = width - textSize.size.width
                                // 如果文本本身比画布还宽，则从0开始绘制
                                if (textX < 0f) textX = 0f
                            }
                            
                            // 计算文本Y坐标，确保在有效范围内
                            val textY = (y - textSize.size.height - 4).coerceIn(0f, height - textSize.size.height)
                            
                            drawText(
                                textMeasurer = textMeasurer,
                                text = depthText,
                                topLeft = Offset(textX, textY),
                                style = textStyle
                            )
                        } catch (e: Exception) {
                            // 忽略单个点的绘制错误，继续处理下一个点
                            Log.e("DepthChart", "绘制超声波数据点出错: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.e("DepthChart", "绘制超声波数据出错: ${e.message}")
                }
            }
            
            // 计算深度图表 - 河床轮廓 - 考虑顶部边距
            if (depthHistory.isNotEmpty() && currentMaxDepth > 0f) {
                try {
                    // 绘制河床轮廓
                    val path = Path()
                    val points = mutableListOf<Offset>()
                    
                    // 确保有足够的点来绘制河床
                    if (depthHistory.size < 2) {
                        // 如果点太少，生成一些基本点
                        points.add(Offset(0f, topMargin + drawableHeight * 0.7f))
                        points.add(Offset(width * 0.25f, topMargin + drawableHeight * 0.65f))
                        points.add(Offset(width * 0.5f, topMargin + drawableHeight * 0.75f))
                        points.add(Offset(width * 0.75f, topMargin + drawableHeight * 0.7f))
                        points.add(Offset(width, topMargin + drawableHeight * 0.8f))
                    } else {
                        try {
                            val step = width / min(depthHistory.size, 100)
                            depthHistory.takeLast(100).forEachIndexed { index, depth ->
                                try {
                                    val x = index * step
                                    // 确保深度值在合理范围内
                                    val validDepth = depth.coerceIn(0f, currentMaxDepth)
                                    // 调整河床高度计算，确保在浅水时也能显示
                                    // 使河床占据屏幕下半部分，不管水深如何
                                    val adjustedDepth = drawableHeight * (0.5f + (validDepth / currentMaxDepth) * 0.4f)
                                    val y = topMargin + adjustedDepth
                                    points.add(Offset(x, y))
                                } catch (e: Exception) {
                                    Log.e("DepthChart", "计算河床点坐标出错: ${e.message}")
                                    // 使用默认值代替
                                    val x = index * step
                                    val y = topMargin + drawableHeight * 0.7f
                                    points.add(Offset(x, y))
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("DepthChart", "生成河床轮廓点出错: ${e.message}")
                            // 如果生成轮廓点出错，使用默认值
                            points.add(Offset(0f, topMargin + drawableHeight * 0.7f))
                            points.add(Offset(width, topMargin + drawableHeight * 0.7f))
                        }
                    }
                    
                    if (points.isNotEmpty()) {
                        try {
                            // 创建平滑的贝塞尔曲线路径
                            val smoothPath = Path()
                            val shadowPath = Path()

                            if (points.size >= 2) {
                                // 使用贝塞尔曲线创建平滑轮廓
                                smoothPath.moveTo(points.first().x, points.first().y)
                                shadowPath.moveTo(points.first().x, points.first().y + 8f) // 阴影偏移

                                for (i in 1 until points.size) {
                                    val currentPoint = points[i]
                                    val previousPoint = points[i - 1]

                                    // 计算控制点以创建平滑曲线
                                    val controlPointX = (previousPoint.x + currentPoint.x) / 2
                                    val controlPointY = (previousPoint.y + currentPoint.y) / 2

                                    // 添加二次贝塞尔曲线
                                    smoothPath.quadraticBezierTo(
                                        controlPointX, previousPoint.y,
                                        currentPoint.x, currentPoint.y
                                    )

                                    // 阴影路径
                                    shadowPath.quadraticBezierTo(
                                        controlPointX, previousPoint.y + 8f,
                                        currentPoint.x, currentPoint.y + 8f
                                    )
                                }
                            } else {
                                // 如果点太少，使用直线
                                smoothPath.moveTo(points.first().x, points.first().y)
                                shadowPath.moveTo(points.first().x, points.first().y + 8f)
                                points.forEach { point ->
                                    smoothPath.lineTo(point.x, point.y)
                                    shadowPath.lineTo(point.x, point.y + 8f)
                                }
                            }

                            // 闭合主路径到底部
                            smoothPath.lineTo(points.last().x, height)
                            smoothPath.lineTo(0f, height)
                            smoothPath.close()

                            // 闭合阴影路径
                            shadowPath.lineTo(points.last().x, height + 8f)
                            shadowPath.lineTo(0f, height + 8f)
                            shadowPath.close()

                            // 绘制阴影层 - 增加深度感
                            drawPath(
                                path = shadowPath,
                                color = Color.Black.copy(alpha = 0.3f)
                            )

                            // 绘制河床多层渐变填充 - 增强立体效果
                            val primaryGradient = Brush.verticalGradient(
                                colors = listOf(
                                    RiverbedSurfaceGold.copy(alpha = 0.95f), // 浅金色 - 河床表面
                                    RiverbedMidOrange.copy(alpha = 0.9f),    // 橙黄色
                                    RiverbedDeepOrange.copy(alpha = 0.9f),   // 橙红色
                                    RiverbedDarkOrange.copy(alpha = 0.85f),  // 深橙色
                                    RiverbedSoilBrown.copy(alpha = 0.9f)     // 棕色 - 深层土壤
                                ),
                                startY = points.minOfOrNull { it.y } ?: topMargin,
                                endY = height
                            )
                            drawPath(smoothPath, brush = primaryGradient)

                            // 添加纹理层 - 模拟沙石纹理
                            val textureGradient = Brush.radialGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    RiverbedSoilBrown.copy(alpha = 0.2f),
                                    Color.Transparent
                                ),
                                radius = width * 0.3f,
                                center = Offset(width * 0.5f, height * 0.8f)
                            )
                            drawPath(smoothPath, brush = textureGradient)

                            // 绘制河床顶线 - 增强发光效果
                            val topLinePath = Path()
                            if (points.size >= 2) {
                                topLinePath.moveTo(points.first().x, points.first().y)
                                for (i in 1 until points.size) {
                                    val currentPoint = points[i]
                                    val previousPoint = points[i - 1]
                                    val controlPointX = (previousPoint.x + currentPoint.x) / 2
                                    val controlPointY = (previousPoint.y + currentPoint.y) / 2

                                    topLinePath.quadraticBezierTo(
                                        controlPointX, previousPoint.y,
                                        currentPoint.x, currentPoint.y
                                    )
                                }
                            } else {
                                topLinePath.moveTo(points.first().x, points.first().y)
                                points.forEach { point ->
                                    topLinePath.lineTo(point.x, point.y)
                                }
                            }

                            // 绘制发光外轮廓
                            drawPath(
                                path = topLinePath,
                                color = RiverbedSurfaceGold.copy(alpha = 0.6f),
                                style = Stroke(width = 6f, cap = StrokeCap.Round, join = StrokeJoin.Round)
                            )

                            // 绘制主轮廓线
                            drawPath(
                                path = topLinePath,
                                color = Color.White,
                                style = Stroke(width = 3f, cap = StrokeCap.Round, join = StrokeJoin.Round)
                            )

                            // 绘制内部高光线
                            drawPath(
                                path = topLinePath,
                                color = Color(0xFFFFF9C4).copy(alpha = 0.8f),
                                style = Stroke(width = 1.5f, cap = StrokeCap.Round, join = StrokeJoin.Round)
                            )

                            // 添加河床表面细节纹理
                            if (points.size >= 4) {
                                for (i in 0 until points.size - 1 step 3) {
                                    val point1 = points[i]
                                    val point2 = points.getOrNull(i + 1) ?: continue

                                    // 绘制小的沙石颗粒效果
                                    repeat(3) { j ->
                                        val particleX = point1.x + (point2.x - point1.x) * (j + 1) / 4f
                                        val particleY = point1.y + (point2.y - point1.y) * (j + 1) / 4f +
                                                       (kotlin.random.Random.nextFloat() - 0.5f) * 8f

                                        drawCircle(
                                            color = RiverbedSoilBrown.copy(alpha = 0.4f),
                                            radius = 1.5f + kotlin.random.Random.nextFloat() * 1f,
                                            center = Offset(particleX, particleY)
                                        )
                                    }
                                }
                            }

                            // 添加河床深度阴影效果
                            val depthShadowPath = Path()
                            if (points.size >= 2) {
                                depthShadowPath.moveTo(points.first().x, points.first().y + 3f)
                                for (i in 1 until points.size) {
                                    val currentPoint = points[i]
                                    val previousPoint = points[i - 1]
                                    val controlPointX = (previousPoint.x + currentPoint.x) / 2
                                    val controlPointY = (previousPoint.y + currentPoint.y) / 2

                                    depthShadowPath.quadraticBezierTo(
                                        controlPointX, previousPoint.y + 3f,
                                        currentPoint.x, currentPoint.y + 3f
                                    )
                                }

                                // 绘制深度阴影线
                                drawPath(
                                    path = depthShadowPath,
                                    color = Color.Black.copy(alpha = 0.4f),
                                    style = Stroke(width = 2f, cap = StrokeCap.Round)
                                )
                            }
                        } catch (e: Exception) {
                            Log.e("DepthChart", "绘制河床路径出错: ${e.message}")
                        }
                        
                        // 在河床轮廓上绘制深度标签 - 调整位置，避免被遮盖
                        val labelInterval = points.size / 4 // 显示4个深度标签
                        if (labelInterval > 0) {
                            for (i in 0 until points.size step labelInterval) {
                                if (i < points.size) {
                                    try {
                                        val point = points[i]
                                        val depthValue = depthHistory.takeLast(100).getOrNull(i)
                                        if (depthValue != null) {
                                            // 确保深度值在合理范围内
                                            val validDepth = depthValue.coerceIn(0f, currentMaxDepth)
                                            val depthText = String.format("%.1fm", validDepth / 100)
                                            val textStyle = TextStyle(
                                                fontSize = 14.sp, // 加大字体
                                                color = Color.White,
                                                fontWeight = FontWeight.Bold,
                                                background = Color.Black.copy(alpha = 0.7f),
                                                textAlign = TextAlign.Center
                                            )
                                            val measuredText = textMeasurer.measure(depthText, textStyle)
                                            
                                            // 计算文本绘制的起始 X 坐标，并确保其在有效范围内
                                            var textX = point.x - measuredText.size.width / 2
                                            // 确保文本的左边缘不会超出画布的左边界 (0f)
                                            if (textX < 0f) {
                                                textX = 0f 
                                            }
                                            // 确保文本的右边缘不会超出画布的右边界 (size.width 来自CanvasScope)
                                            if (textX + measuredText.size.width > size.width) { 
                                                textX = size.width - measuredText.size.width
                                                // 如果文本本身比画布还宽，则从0开始绘制，避免textX为负
                                                if (textX < 0f) textX = 0f 
                                            }

                                            val textY = point.y - measuredText.size.height - 4
                                            
                                            // 将标签绘制在河床线上方，避免被热力图遮挡
                                            drawText(
                                                textLayoutResult = measuredText,
                                                topLeft = Offset(textX, textY)
                                            )
                                        }
                                    } catch (e: Exception) {
                                        Log.e("DepthChart", "绘制河床深度标签出错: ${e.message}")
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e("DepthChart", "绘制河床轮廓出错: ${e.message}")
                }
                
                // 绘制鱼群图标并添加深度标签 - 考虑顶部边距
                fishData.forEach { fish ->
                    try {
                        // 确保鱼的位置和深度在合理范围内
                        val validX = fish.x.coerceIn(0f, 100f)
                        val validDepth = fish.depth.coerceIn(0f, currentMaxDepth)
                        val validSize = fish.size.coerceIn(1, 3)
                        
                        val fishX = (validX / 100f) * width
                        
                        // 调整鱼的深度，以匹配调整后的河床高度，并考虑顶部边距
                        val fishDepthPercentage = validDepth / currentMaxDepth
                        // 修改鱼群位置计算方式，使用与水深标尺相同的线性映射
                        val fishY = topMargin + (validDepth / currentMaxDepth) * drawableHeight
                        
                        // 根据鱼的大小决定图标大小和颜色
                        val fishSize = when (validSize) {
                            1 -> 18.dp.toPx() // 小型鱼
                            2 -> 25.dp.toPx() // 中型鱼
                            3 -> 35.dp.toPx() // 大型鱼
                            else -> 18.dp.toPx() // 默认大小
                        }
                        
                        // 根据鱼的大小选择颜色
                        val fishColor = when (validSize) {
                            1 -> Color(0xFF8CBFD9) // 浅蓝色小鱼
                            2 -> Color(0xFF3FBFBF) // 青色中鱼
                            3 -> Color(0xFFFFD700) // 金色大鱼
                            else -> FishIconWhite
                        }
                        
                        // 绘制鱼形图标
                        drawPath(
                            path = Path().apply {
                                // 鱼身 - 椭圆形
                                addOval(
                                    Rect(
                                        left = fishX - fishSize,
                                        top = fishY - fishSize / 3,
                                        right = fishX,
                                        bottom = fishY + fishSize / 3
                                    )
                                )
                                
                                // 鱼尾 - 三角形
                                moveTo(fishX - fishSize, fishY)
                                lineTo(fishX - fishSize * 1.5f, fishY - fishSize / 2)
                                lineTo(fishX - fishSize * 1.5f, fishY + fishSize / 2)
                                close()
                                
                                // 鱼鳍 - 上
                                moveTo(fishX - fishSize * 0.5f, fishY - fishSize / 3)
                                lineTo(fishX - fishSize * 0.7f, fishY - fishSize * 0.7f)
                                lineTo(fishX - fishSize * 0.3f, fishY - fishSize / 3)
                                close()
                                
                                // 鱼鳍 - 下
                                moveTo(fishX - fishSize * 0.5f, fishY + fishSize / 3)
                                lineTo(fishX - fishSize * 0.7f, fishY + fishSize * 0.7f)
                                lineTo(fishX - fishSize * 0.3f, fishY + fishSize / 3)
                                close()
                            },
                            color = fishColor
                        )
                        
                        // 绘制鱼眼
                        drawCircle(
                            color = Color.Black,
                            radius = fishSize / 8,
                            center = Offset(fishX - fishSize * 0.3f, fishY - fishSize * 0.1f)
                        )
                        
                        // 绘制鱼眼高光
                        drawCircle(
                            color = Color.White,
                            radius = fishSize / 16,
                            center = Offset(fishX - fishSize * 0.25f, fishY - fishSize * 0.15f)
                        )
                        
                        // 绘制鱼的深度标签 - 优化显示位置，避免被遮盖
                        val depthText = String.format("%.1fm", validDepth / 100) // 转换为米
                        val textStyle = TextStyle(
                            fontSize = 10.sp, // 加大字体
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            background = Color.Black.copy(alpha = 0.7f),
                            textAlign = TextAlign.Left
                        )
                        val textSize = textMeasurer.measure(depthText, textStyle)
                        
                        // 根据鱼的位置动态调整标签位置
                        var labelX = fishX + 5
                        // 确保文本不会超出右边界
                        if (labelX + textSize.size.width > width) {
                            labelX = width - textSize.size.width
                        }
                        
                        var labelY = if (fishY < height * 0.5f) {
                            // 上半部分鱼，标签显示在下方
                            fishY + textSize.size.height / 2 + 5
                        } else {
                            // 下半部分鱼，标签显示在上方
                            fishY - textSize.size.height - 5
                        }
                        
                        // 确保文本Y坐标在有效范围内
                        labelY = labelY.coerceIn(0f, height - textSize.size.height)
                        
                        drawText(
                            textMeasurer = textMeasurer,
                            text = depthText,
                            topLeft = Offset(labelX, labelY),
                            style = textStyle
                        )
                    } catch (e: Exception) {
                        Log.e("DepthChart", "绘制鱼群图标出错: ${e.message}")
                    }
                }
            }
            
            // 绘制深度水平刻度线和标尺 - 以0.5米为基准
            // 注意：此代码块被移到了河床轮廓绘制之后，这样水深标尺会显示在河床图的上方
            for (i in 0..depthLabelCount) {
                // 计算Y坐标，加上顶部边距，确保标尺完全显示
                val depth = i * depthInterval
                val y = topMargin + (depth / currentMaxDepth) * drawableHeight
                
                // 绘制主要刻度线
                drawLine(
                    color = Color.White.copy(alpha = 0.5f),
                    start = Offset(0f, y),
                    end = Offset(width, y),
                    strokeWidth = 1.5f
                )
                
                // 绘制刻度标记 - 左侧
                drawLine(
                    color = Color.White,
                    start = Offset(0f, y),
                    end = Offset(10f, y),
                    strokeWidth = 2.5f, // 加粗刻度线
                    cap = StrokeCap.Round
                )
                
                // 绘制刻度标记 - 右侧
                drawLine(
                    color = Color.White,
                    start = Offset(width - 10f, y),
                    end = Offset(width, y),
                    strokeWidth = 2.5f, // 加粗刻度线
                    cap = StrokeCap.Round
                )
                
                // 在左侧绘制深度标签 - 加大加粗字体
                val depthText = String.format("%.1fm", depth / 100) // 转换为米
                val textStyle = TextStyle(
                    fontSize = 16.sp, // 加大字体
                    color = Color.White,
                    fontWeight = FontWeight.ExtraBold, // 加粗字体
                    textAlign = TextAlign.Left
                )
                val textSize = textMeasurer.measure(depthText, textStyle)
                
                // 特殊处理最后一个标签，确保它完全显示
                val textY = if (i == depthLabelCount) {
                    y - textSize.size.height
                } else {
                    y - textSize.size.height / 2
                }
                
                drawText(
                    textMeasurer = textMeasurer,
                    text = depthText,
                    topLeft = Offset(15f, textY),
                    style = textStyle
                )
            }
            
            // 如果超出5米探测范围，显示提示信息
            if (isExceedingRange) {
                val warningText = "超出5M探测范围"
                val warningStyle = TextStyle(
                    fontSize = 18.sp,
                    color = Color.Red,
                    fontWeight = FontWeight.Bold,
                    background = Color.Black.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center
                )
                val warningTextSize = textMeasurer.measure(warningText, warningStyle)
                
                // 在底部中央显示警告信息
                drawText(
                    textMeasurer = textMeasurer,
                    text = warningText,
                    topLeft = Offset(
                        (width - warningTextSize.size.width) / 2,
                        height - warningTextSize.size.height - 20f
                    ),
                    style = warningStyle
                )
            }
        }
    }
} 