package com.example.lnnfilsh.viewmodel

import android.app.Application
import android.content.Context
import android.util.Log
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.lnnfilsh.model.Command
import com.example.lnnfilsh.model.DeviceStatus
import com.example.lnnfilsh.model.FishData
import com.example.lnnfilsh.model.FishSize
import com.example.lnnfilsh.model.SensorData
import com.example.lnnfilsh.model.SensorDataType
import com.example.lnnfilsh.network.TcpClient
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.sin
import kotlin.random.Random
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FishDetectorViewModel(application: Application) : ViewModel() {
    // SharedPreferences实例
    private val sharedPreferences = application.getSharedPreferences("fish_finder_prefs", Context.MODE_PRIVATE)
    
    // TCP客户端实例
    private val tcpClient = TcpClient()
    
    // UI状态
    private val _connectionStatus = MutableStateFlow(false)
    val connectionStatus: StateFlow<Boolean> = _connectionStatus
    
    private val _currentDepth = MutableStateFlow(150f)
    val currentDepth: StateFlow<Float> = _currentDepth
    
    private val _waterTemperature = MutableStateFlow(18.5f)
    val waterTemperature: StateFlow<Float> = _waterTemperature
    
    private val _batteryPercentage = MutableStateFlow(100)
    val batteryPercentage: StateFlow<Int> = _batteryPercentage
    
    private val _ledStatus = MutableStateFlow(false)
    val ledStatus: StateFlow<Boolean> = _ledStatus
    
    // 深度历史数据（用于绘制图表）
    private val _depthHistory = MutableStateFlow<List<Float>>(emptyList())
    val depthHistory: StateFlow<List<Float>> = _depthHistory
    
    // 鱼群数据
    private val _fishData = MutableStateFlow<List<FishData>>(emptyList())
    val fishData: StateFlow<List<FishData>> = _fishData
    
    // 灵敏度设置 (0.0 - 1.0)
    private val _sensitivity = MutableStateFlow(5f)
    val sensitivity: StateFlow<Float> = _sensitivity
    
    // 深度阈值（触发警报的深度）
    private val _depthThreshold = MutableStateFlow(200)
    val depthThreshold: StateFlow<Int> = _depthThreshold
    
    // 采样间隔（毫秒）
    private val _samplingInterval = MutableStateFlow(50)
    val samplingInterval: StateFlow<Int> = _samplingInterval
    
    // 是否正在收集数据
    private val _isCollecting = MutableStateFlow(false)
    val isCollecting: StateFlow<Boolean> = _isCollecting
    
    // 是否启用振动
    private val _vibrationEnabled = MutableStateFlow(sharedPreferences.getBoolean("vibration_enabled", true))
    val vibrationEnabled: StateFlow<Boolean> = _vibrationEnabled

    // 显示样式状态 - 0: 真实水底, 1: 声呐模式
    private val _displayStyle = MutableStateFlow(sharedPreferences.getInt("display_style", 0))
    val displayStyle: StateFlow<Int> = _displayStyle
    
    // 超声波探测数据
    private val _ultrasonicData = MutableStateFlow<List<Float>>(emptyList())
    val ultrasonicData: StateFlow<List<Float>> = _ultrasonicData
    
    // 方向控制状态
    private val _isMovingForward = MutableStateFlow(false)
    val isMovingForward: StateFlow<Boolean> = _isMovingForward
    
    private val _isMovingBackward = MutableStateFlow(false)
    val isMovingBackward: StateFlow<Boolean> = _isMovingBackward
    
    private val _isTurningLeft = MutableStateFlow(false)
    val isTurningLeft: StateFlow<Boolean> = _isTurningLeft
    
    private val _isTurningRight = MutableStateFlow(false)
    val isTurningRight: StateFlow<Boolean> = _isTurningRight
    
    // 方向按钮显示状态
    private val _showDirectionButtons = MutableStateFlow(sharedPreferences.getBoolean("show_direction_buttons", true))
    val showDirectionButtons: StateFlow<Boolean> = _showDirectionButtons
    
    // 警报状态
    private val _alarmActive = MutableStateFlow(false)
    val alarmActive: StateFlow<Boolean> = _alarmActive
    
    // 用于标记是否是首次连接后收到数据
    private var isFirstDataAfterConnection = true
    
    // 新增：用于向UI报告错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage
    
    // 生成初始深度数据
    private fun generateInitialDepthData(): List<Float> {
        val result = mutableListOf<Float>()
        
        // 生成逼真的河床轮廓，最大深度5米
        val baseDepth = 200f // 基础深度2米
        val maxDepth = 500f // 最大深度5米
        
        for (i in 0 until 100) {
            // 使用正弦函数生成波浪形河床
            val depth = baseDepth + (maxDepth - baseDepth) * 
                        (0.5f + 0.3f * kotlin.math.sin(i / 10f) + 
                         0.2f * kotlin.math.sin(i / 5f) + 
                         0.1f * Random.nextFloat())
            
            result.add(depth.coerceIn(100f, maxDepth))
        }
        
        return result
    }
    
    // 生成初始鱼群数据
    private fun generateInitialFishData(): List<FishData> {
        val result = mutableListOf<FishData>()
        val depthData = _depthHistory.value
        
        if (depthData.isEmpty()) return emptyList()
        
        // 模拟几条鱼，处于不同深度
        val fishCount = Random.nextInt(5, 10)
        val depths = listOf(
            110f, 150f, 210f, 230f, 280f, 320f, 410f, 460f
        ) // 常见的鱼群深度，以厘米为单位
        
        for (i in 0 until fishCount) {
            val x = Random.nextFloat() * 100 // x坐标 (0-100)
            val depth = depths[i % depths.size] + Random.nextFloat() * 20 - 10 // 随机微调深度
            val size = when {
                depth < 200f -> 1 // 小型鱼
                depth < 350f -> 2 // 中型鱼
                else -> 3 // 大型鱼
            }
            
            result.add(FishData(depth, size, 90f, x, 0f))
        }
        
        return result
    }
    
    // 初始化，设置收集器
    init {
        viewModelScope.launch {
            // 监听连接状态
            tcpClient.connectionStatusFlow.collect { isConnected ->
                _connectionStatus.value = isConnected
                if (isConnected) {
                    // 连接成功时自动开始收集数据，并重置首次数据标记
                    isFirstDataAfterConnection = true
                    // 清空现有数据，等待真实数据到达
                    _depthHistory.value = emptyList()
                    _fishData.value = emptyList()
                    _ultrasonicData.value = emptyList()
                    startCollecting()
                }
            }
        }
        
        viewModelScope.launch {
            // 监听传感器数据
            tcpClient.sensorDataFlow.collect { sensorData ->
                try {
                    when (sensorData.type) {
                        SensorDataType.DEPTH -> {
                            try {
                                val depth = sensorData.values?.get("depth") ?: 0f
                                val temp = sensorData.values?.get("temperature") ?: 0f
                                
                                // 数据合理性验证
                                val validDepth = if (depth < 0f || depth > 2000f) 150f else depth
                                val validTemp = if (temp < -20f || temp > 70f) 18.5f else temp
                                
                                _currentDepth.value = validDepth
                                _waterTemperature.value = validTemp
                                
                                // 更新深度历史
                                val history = if (isFirstDataAfterConnection) {
                                    // 如果是连接后的第一个数据，则清空历史数据并使用新数据
                                    isFirstDataAfterConnection = false
                                    mutableListOf(validDepth)
                                } else {
                                    _depthHistory.value.toMutableList().apply { add(validDepth) }
                                }
                                
                                if (history.size > 100) { // 保留最近100个数据点
                                    history.removeAt(0)
                                }
                                _depthHistory.value = history
                                
                                // 检查是否触发警报
                                checkAlarmTrigger(validDepth)
                                
                                // 处理超声波数据点
                                val ultrasonicPoints = mutableListOf<Float>()
                                for (i in 0 until 8) {
                                    val pointKey = "point$i"
                                    if (sensorData.values?.containsKey(pointKey) == true) {
                                        val pointValue = sensorData.values[pointKey] ?: 0f
                                        // 验证超声波数据点的有效性
                                        val validPoint = if (pointValue < 0f || pointValue > 2000f) 0f else pointValue
                                        ultrasonicPoints.add(validPoint)
                                    }
                                }
                                
                                // 如果找到超声波数据点，更新状态
                                if (ultrasonicPoints.isNotEmpty()) {
                                    _ultrasonicData.value = ultrasonicPoints
                                }
                            } catch (e: Exception) {
                                Log.e("FishDetectorViewModel", "处理深度数据时出错: ${e.message}", e)
                                _errorMessage.value = "处理深度数据时出错: ${e.message}"
                            }
                        }
                        SensorDataType.FISH -> {
                            try {
                                // 处理鱼群数据
                                val fishDepth = sensorData.values?.get("depth") ?: 0f
                                val fishSize = (sensorData.values?.get("size") ?: 1f).toInt()
                                val confidence = sensorData.values?.get("confidence") ?: 0f
                                
                                // 数据合理性验证
                                val validDepth = if (fishDepth < 0f || fishDepth > 2000f) 150f else fishDepth
                                val validSize = fishSize.coerceIn(1, 3) // 确保鱼的大小在1-3之间
                                val validConfidence = confidence.coerceIn(0f, 100f)
                                
                                // 只处理置信度足够高的鱼群数据
                                if (validConfidence > 60) {
                                    val x = Random.nextFloat() * 100 // 随机水平位置
                                    val newFish = FishData(validDepth, validSize, validConfidence, x, 0f)
                                    
                                    // 添加到鱼群列表中
                                    val updatedFishList = _fishData.value.toMutableList()
                                    updatedFishList.add(newFish)
                                    
                                    // 保持鱼的数量在合理范围内
                                    if (updatedFishList.size > 15) {
                                        updatedFishList.removeAt(0)
                                    }
                                    
                                    _fishData.value = updatedFishList
                                }
                            } catch (e: Exception) {
                                Log.e("FishDetectorViewModel", "处理鱼群数据时出错: ${e.message}", e)
                                _errorMessage.value = "处理鱼群数据时出错: ${e.message}"
                            }
                        }
                        SensorDataType.ULTRASONIC -> {
                            try {
                                // 处理专用超声波数据类型
                                val points = mutableListOf<Float>()
                                
                                // 确保sensorData.values存在
                                sensorData.values?.forEach { (key, value) ->
                                    if (key.startsWith("point")) {
                                        // 验证数据点有效性
                                        val validValue = if (value < 0f || value > 2000f) 0f else value
                                        points.add(validValue)
                                    }
                                }
                                
                                if (points.isNotEmpty()) {
                                    _ultrasonicData.value = points
                                }
                            } catch (e: Exception) {
                                Log.e("FishDetectorViewModel", "处理超声波数据时出错: ${e.message}", e)
                                _errorMessage.value = "处理超声波数据时出错: ${e.message}"
                            }
                        }
                        SensorDataType.UltrasonicDistance -> {
                            try {
                                // 数据合理性验证
                                val validValue = if (sensorData.value < 0f || sensorData.value > 2000f) 150f else sensorData.value
                                
                                // 兼容旧版本数据
                                _currentDepth.value = validValue
                                
                                // 更新深度历史
                                val history = if (isFirstDataAfterConnection) {
                                    // 如果是连接后的第一个数据，则清空历史数据并使用新数据
                                    isFirstDataAfterConnection = false
                                    mutableListOf(validValue)
                                } else {
                                    _depthHistory.value.toMutableList().apply { add(validValue) }
                                }
                                
                                if (history.size > 100) {
                                    history.removeAt(0)
                                }
                                _depthHistory.value = history
                                
                                checkAlarmTrigger(validValue)
                            } catch (e: Exception) {
                                Log.e("FishDetectorViewModel", "处理旧版本距离数据时出错: ${e.message}", e)
                                _errorMessage.value = "处理距离数据时出错: ${e.message}"
                            }
                        }
                        SensorDataType.Temperature -> {
                            try {
                                // 数据合理性验证
                                val validTemp = if (sensorData.value < -20f || sensorData.value > 70f) 18.5f else sensorData.value
                                
                                // 兼容旧版本数据
                                _waterTemperature.value = validTemp
                            } catch (e: Exception) {
                                Log.e("FishDetectorViewModel", "处理温度数据时出错: ${e.message}", e)
                                _errorMessage.value = "处理温度数据时出错: ${e.message}"
                            }
                        }
                        else -> { /* 忽略其他类型 */ }
                    }
                } catch (e: Exception) {
                    Log.e("FishDetectorViewModel", "处理传感器数据时发生错误: ${e.message}", e)
                    _errorMessage.value = "处理数据时出错: ${e.message}"
                }
            }
        }
        
        viewModelScope.launch {
            // 监听设备状态
            tcpClient.deviceStatusFlow.collect { status ->
                _ledStatus.value = status.ledStatus
                _batteryPercentage.value = status.batteryPercentage
                _isCollecting.value = status.isCollecting
            }
        }
    }
    
    // 连接到设备
    fun connect() {
        // 建立实际连接
        tcpClient.connect()
        
        // 注意：这里不再自动将连接状态设为true
        // 也不再启动演示数据生成，而是等待真实连接成功
        // _connectionStatus.value 将由tcpClient.connectionStatusFlow的监听器更新
    }
    
    // 启动演示数据生成 - 仅用于演示模式
    fun startDemoMode() {
        // 设置为演示模式
        _connectionStatus.value = true
        
        viewModelScope.launch {
            try {
                var lastTime = System.currentTimeMillis()
                
                while (_connectionStatus.value) {
                    // 控制更新频率，避免过于频繁的更新
                    val currentTime = System.currentTimeMillis()
                    val elapsed = currentTime - lastTime
                    if (elapsed < _samplingInterval.value) {
                        delay(_samplingInterval.value - elapsed)
                    }
                    lastTime = System.currentTimeMillis()
                    
                    // 模拟水温缓慢变化
                    _waterTemperature.value += (Random.nextFloat() * 0.2f - 0.1f)
                    _waterTemperature.value = _waterTemperature.value.coerceIn(16f, 22f)
                    
                    // 模拟深度随时间波动
                    val currentDepth = _currentDepth.value
                    val newDepth = (currentDepth + Random.nextFloat() * 20 - 10).coerceIn(100f, 500f)
                    _currentDepth.value = newDepth
                    
                    // 更新深度历史
                    val history = _depthHistory.value.toMutableList()
                    history.add(newDepth)
                    if (history.size > 100) {
                        history.removeAt(0)
                    }
                    _depthHistory.value = history
                    
                    // 如果正在收集数据，模拟鱼群
                    if (_isCollecting.value) {
                        simulateFishData(newDepth)
                    }
                    
                    // 模拟超声波数据
                    if (_isCollecting.value && Random.nextFloat() < 0.3f) { // 30%概率更新超声波数据
                        simulateUltrasonicData(newDepth)
                    }
                }
            } catch (e: Exception) {
                // 捕获并记录任何异常，防止崩溃
                Log.e("FishDetectorViewModel", "演示数据生成出错: ${e.message}")
            }
        }
    }
    
    // 模拟超声波数据
    private fun simulateUltrasonicData(currentDepth: Float) {
        try {
            // 生成8个超声波数据点
            val points = mutableListOf<Float>()
            
            // 基于当前深度生成一条曲线
            val baseDepth = currentDepth * 0.8f // 基础深度为当前深度的80%
            
            for (i in 0 until 8) {
                // 生成一个波浪形的深度曲线
                val pointDepth = baseDepth + 
                                (currentDepth * 0.3f * sin(i / 2.0f)) + 
                                (Random.nextFloat() * 20f - 10f) // 添加随机波动
                
                points.add(pointDepth.coerceIn(50f, 500f))
            }
            
            // 更新超声波数据
            _ultrasonicData.value = points
        } catch (e: Exception) {
            Log.e("FishDetectorViewModel", "模拟超声波数据出错: ${e.message}")
        }
    }
    
    // 断开连接
    fun disconnect() {
        _connectionStatus.value = false
        tcpClient.disconnect()
    }
    
    // 开始采集数据
    fun startCollecting() {
        if (!_isCollecting.value) {
            _isCollecting.value = true
            tcpClient.startCollecting()
        }
    }
    
    // 停止采集数据
    fun stopCollecting() {
        if (_isCollecting.value) {
            _isCollecting.value = false
            tcpClient.stopCollecting()
        }
    }
    
    // 重置设备
    fun resetDevice() {
        tcpClient.resetDevice()
    }
    
    // 更新灵敏度
    fun updateSensitivity(newValue: Float) {
        // 先记录原始值，以便发送命令失败时恢复
        val oldValue = _sensitivity.value
        
        // 预先更新UI状态，提供即时反馈
        _sensitivity.value = newValue
        
        // 启动协程发送命令，避免阻塞UI线程
        viewModelScope.launch(Dispatchers.IO) {
            // 发送更新的灵敏度和采样间隔到设备
            val success = tcpClient.setParameters(newValue.toInt(), _samplingInterval.value)
            
            // 如果发送失败，恢复原始值
            if (!success) {
                withContext(Dispatchers.Main) {
                    _sensitivity.value = oldValue
                    _errorMessage.value = "设置灵敏度失败，请稍后重试"
                    delay(2000)
                    _errorMessage.value = null
                }
            }
        }
    }
    
    // 更新深度阈值
    fun updateDepthThreshold(newValue: Int) {
        _depthThreshold.value = newValue
    }
    
    // 更新采样间隔
    fun updateSamplingInterval(newValue: Int) {
        // 先记录原始值，以便发送命令失败时恢复
        val oldValue = _samplingInterval.value
        
        // 预先更新UI状态，提供即时反馈
        _samplingInterval.value = newValue
        
        // 如果正在收集数据，更新采样间隔
        if (_isCollecting.value) {
            // 启动协程发送命令，避免阻塞UI线程
            viewModelScope.launch(Dispatchers.IO) {
                val success = tcpClient.setParameters(_sensitivity.value.toInt(), newValue)
                
                // 如果发送失败，恢复原始值
                if (!success) {
                    withContext(Dispatchers.Main) {
                        _samplingInterval.value = oldValue
                        _errorMessage.value = "设置采样间隔失败，请稍后重试"
                        delay(2000)
                        _errorMessage.value = null
                    }
                }
            }
        }
    }
    
    // 切换LED灯状态
    fun toggleLed() {
        // 先记录原始状态，以便发送命令失败时恢复
        val oldStatus = _ledStatus.value
        
        // 预先更新UI状态，提供即时反馈
        _ledStatus.value = !oldStatus
        
        // 启动协程发送命令，避免阻塞UI线程
        viewModelScope.launch(Dispatchers.IO) {
            val success = tcpClient.toggleLed()
            
            // 如果发送失败，恢复原始状态
            if (!success) {
                withContext(Dispatchers.Main) {
                    _ledStatus.value = oldStatus
                    _errorMessage.value = "切换LED灯失败，请稍后重试"
                    delay(2000)
                    _errorMessage.value = null
                }
            }
        }
    }
    
    // 方向控制方法
    fun moveForward(isMoving: Boolean) {
        _isMovingForward.value = isMoving
        if (isMoving) {
            tcpClient.sendCommand(Command.MOVE_FORWARD)
        } else {
            tcpClient.sendCommand(Command.STOP)
        }
    }
    
    fun moveBackward(isMoving: Boolean) {
        _isMovingBackward.value = isMoving
        if (isMoving) {
            tcpClient.sendCommand(Command.MOVE_BACKWARD)
        } else {
            tcpClient.sendCommand(Command.STOP)
        }
    }
    
    fun turnLeft(isTurning: Boolean) {
        _isTurningLeft.value = isTurning
        if (isTurning) {
            tcpClient.sendCommand(Command.TURN_LEFT)
        } else {
            tcpClient.sendCommand(Command.STOP)
        }
    }
    
    fun turnRight(isTurning: Boolean) {
        _isTurningRight.value = isTurning
        if (isTurning) {
            tcpClient.sendCommand(Command.TURN_RIGHT)
        } else {
            tcpClient.sendCommand(Command.STOP)
        }
    }
    
    // 模拟鱼群数据（基于深度变化）
    private fun simulateFishData(currentDepth: Float) {
        try {
            val depthHistory = _depthHistory.value
            if (depthHistory.size < 2) return
            
            // 计算深度变化率
            val lastDepth = depthHistory[depthHistory.size - 2]
            val depthChange = abs(currentDepth - lastDepth)
            
            // 基于灵敏度和深度变化决定是否生成鱼群数据
            val threshold = (11.0 - _sensitivity.value) * 0.5 // 灵敏度越高，阈值越低
            
            // 检查是否应该刷新鱼群数据
            val shouldUpdateFish = depthChange > threshold || 
                                Random.nextFloat() < 0.02 || // 小概率随机刷新
                                _fishData.value.isEmpty() // 如果没有鱼，则生成一些
            
            if (shouldUpdateFish) {
                // 生成鱼群 - 根据图片样式
                val fishCount = Random.nextInt(1, 5) // 减少鱼的数量，避免过多对象创建
                val newFishList = mutableListOf<FishData>()
                
                // 可能的鱼深度层级（按示例图分布）
                val depthLayers = listOf(
                    110f to 150f, // 浅层鱼 (1.1-1.5m)
                    200f to 280f, // 中层鱼 (2.0-2.8m)
                    400f to 480f  // 深层鱼 (4.0-4.8m)
                )
                
                for (i in 0 until fishCount) {
                    // 随机选择一个深度层
                    val (minDepth, maxDepth) = depthLayers[Random.nextInt(depthLayers.size)]
                    val fishDepth = minDepth + Random.nextFloat() * (maxDepth - minDepth)
                    
                    val x = Random.nextFloat() * 100 // x坐标 (0-100)
                    
                    // 基于深度决定鱼的大小
                    val size = when {
                        fishDepth < 180f -> 1 // 小型鱼
                        fishDepth < 380f -> 2 // 中型鱼
                        else -> 3 // 大型鱼
                    }
                    
                    // 置信度随机值(75-95)
                    val confidence = 75f + Random.nextFloat() * 20
                    
                    newFishList.add(FishData(fishDepth, size, confidence, x, 0f))
                }
                
                // 更新鱼群数据
                _fishData.value = newFishList
            }
        } catch (e: Exception) {
            Log.e("FishDetectorViewModel", "模拟鱼群数据出错: ${e.message}")
        }
    }
    
    // 检查是否触发警报
    private fun checkAlarmTrigger(depth: Float) {
        if (depth < _depthThreshold.value) {
            // 触发警报（在实际应用中可以添加振动或声音）
            // 这里只是打印日志
        }
    }
    
    // 更新方向按钮显示状态
    fun updateDirectionButtonsVisibility(visible: Boolean) {
        _showDirectionButtons.value = visible
        // 将设置保存到SharedPreferences
        sharedPreferences.edit().putBoolean("show_direction_buttons", visible).apply()
    }
    
    // 更新振动开关状态
    fun updateVibrationEnabled(enabled: Boolean) {
        _vibrationEnabled.value = enabled
        // 将设置保存到SharedPreferences
        sharedPreferences.edit().putBoolean("vibration_enabled", enabled).apply()
    }

    // 更新显示样式
    fun updateDisplayStyle(style: Int) {
        _displayStyle.value = style
        // 将设置保存到SharedPreferences
        sharedPreferences.edit().putInt("display_style", style).apply()
    }
    
    // 清除错误消息
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    // 更新错误消息
    fun updateErrorMessage(message: String) {
        _errorMessage.value = message
    }
    
    // 清除ViewModel时断开连接
    override fun onCleared() {
        super.onCleared()
        // 确保断开连接并清理资源
        _connectionStatus.value = false
        tcpClient.disconnect()
        
        // 取消所有协程
        viewModelScope.cancel()
    }
} 