<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\anAPP\filsh\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\anAPP\filsh\app\src\main\res"><file name="da" path="D:\anAPP\filsh\app\src\main\res\drawable\da.png" qualifiers="" type="drawable"/><file name="da_vector" path="D:\anAPP\filsh\app\src\main\res\drawable\da_vector.xml" qualifiers="" type="drawable"/><file name="ic_arrow_down" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_arrow_down.xml" qualifiers="" type="drawable"/><file name="ic_arrow_left" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_arrow_left.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_arrow_up" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_arrow_up.xml" qualifiers="" type="drawable"/><file name="ic_battery" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_battery.xml" qualifiers="" type="drawable"/><file name="ic_battery_25" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_battery_25.xml" qualifiers="" type="drawable"/><file name="ic_battery_50" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_battery_50.xml" qualifiers="" type="drawable"/><file name="ic_battery_75" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_battery_75.xml" qualifiers="" type="drawable"/><file name="ic_battery_full" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_battery_full.xml" qualifiers="" type="drawable"/><file name="ic_battery_low" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_battery_low.xml" qualifiers="" type="drawable"/><file name="ic_bluetooth" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_bluetooth.xml" qualifiers="" type="drawable"/><file name="ic_depth" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_depth.xml" qualifiers="" type="drawable"/><file name="ic_direction_controls" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_direction_controls.xml" qualifiers="" type="drawable"/><file name="ic_fish" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_fish.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_screenshot" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_screenshot.xml" qualifiers="" type="drawable"/><file name="ic_temperature" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_temperature.xml" qualifiers="" type="drawable"/><file name="ic_vibration" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_vibration.xml" qualifiers="" type="drawable"/><file name="ic_wifi" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_wifi.xml" qualifiers="" type="drawable"/><file name="xiao" path="D:\anAPP\filsh\app\src\main\res\drawable\xiao.png" qualifiers="" type="drawable"/><file name="xiao_vector" path="D:\anAPP\filsh\app\src\main\res\drawable\xiao_vector.xml" qualifiers="" type="drawable"/><file name="zhong" path="D:\anAPP\filsh\app\src\main\res\drawable\zhong.png" qualifiers="" type="drawable"/><file name="zhong_vector" path="D:\anAPP\filsh\app\src\main\res\drawable\zhong_vector.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\anAPP\filsh\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="ic_launcher" path="D:\anAPP\filsh\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\anAPP\filsh\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\anAPP\filsh\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\anAPP\filsh\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\anAPP\filsh\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\anAPP\filsh\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\anAPP\filsh\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\anAPP\filsh\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\anAPP\filsh\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\anAPP\filsh\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\anAPP\filsh\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\anAPP\filsh\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="beep" path="D:\anAPP\filsh\app\src\main\res\raw\beep.MP3" qualifiers="" type="raw"/><file path="D:\anAPP\filsh\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\anAPP\filsh\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">探鱼器</string></file><file path="D:\anAPP\filsh\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Lnnfilsh" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="D:\anAPP\filsh\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\anAPP\filsh\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\anAPP\filsh\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="ic_radar" path="D:\anAPP\filsh\app\src\main\res\drawable\ic_radar.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\anAPP\filsh\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\anAPP\filsh\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\anAPP\filsh\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\anAPP\filsh\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>