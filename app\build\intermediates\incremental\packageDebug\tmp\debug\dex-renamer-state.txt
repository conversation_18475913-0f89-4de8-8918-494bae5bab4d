#Thu May 29 19:20:58 CST 2025
base.0=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\12\\classes.dex
base.3=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
base.4=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.5=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.6=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
base.7=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.8=D\:\\anAPP\\filsh\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=12/classes.dex
path.3=14/classes.dex
path.4=3/classes.dex
path.5=5/classes.dex
path.6=7/classes.dex
path.7=8/classes.dex
path.8=classes2.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
