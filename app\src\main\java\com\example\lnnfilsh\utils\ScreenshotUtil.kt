package com.example.lnnfilsh.utils

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.View
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 截图工具类，用于截取当前屏幕并分享
 */
object ScreenshotUtil {
    private const val TAG = "ScreenshotUtil"
    
    /**
     * 截取指定View的图像并分享
     */
    fun takeScreenshotAndShare(context: Context, view: View) {
        try {
            // 创建Bitmap
            val bitmap = takeScreenshot(view)
            
            // 保存并分享
            saveAndShareBitmap(context, bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "截图分享失败: ${e.message}", e)
        }
    }
    
    /**
     * 截取指定View的图像
     */
    private fun takeScreenshot(view: View): Bitmap {
        // 确保View已绘制完成
        view.isDrawingCacheEnabled = true
        view.buildDrawingCache(true)
        
        // 创建Bitmap
        val bitmap = Bitmap.createBitmap(view.drawingCache)
        
        // 清理缓存
        view.isDrawingCacheEnabled = false
        
        return bitmap
    }
    
    /**
     * 保存Bitmap到文件并分享
     */
    private fun saveAndShareBitmap(context: Context, bitmap: Bitmap) {
        try {
            // 生成文件名
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "鱼探仪截图_$timeStamp.jpg"
            
            // 获取Uri和输出流
            val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10及以上使用MediaStore
                saveImageToMediaStore(context, bitmap, fileName)
            } else {
                // Android 9及以下使用文件系统
                saveImageToExternalStorage(context, bitmap, fileName)
            }
            
            // 分享图片
            shareImage(context, uri, fileName)
        } catch (e: Exception) {
            Log.e(TAG, "保存分享图片失败: ${e.message}", e)
        }
    }
    
    /**
     * Android 10及以上，使用MediaStore保存图片
     */
    private fun saveImageToMediaStore(context: Context, bitmap: Bitmap, fileName: String): Uri {
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
        }
        
        val uri = context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
            ?: throw IllegalStateException("无法创建媒体URI")
        
        context.contentResolver.openOutputStream(uri)?.use { outputStream ->
            if (!bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)) {
                throw IllegalStateException("无法保存位图")
            }
        } ?: throw IllegalStateException("无法打开输出流")
        
        return uri
    }
    
    /**
     * Android 9及以下，使用文件系统保存图片
     */
    private fun saveImageToExternalStorage(context: Context, bitmap: Bitmap, fileName: String): Uri {
        val imagesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val imageFile = File(imagesDir, fileName)
        
        FileOutputStream(imageFile).use { outputStream ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
        }
        
        // 使用FileProvider获取Uri
        return FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            imageFile
        )
    }
    
    /**
     * 分享图片
     */
    private fun shareImage(context: Context, uri: Uri, fileName: String) {
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "image/jpeg"
            putExtra(Intent.EXTRA_STREAM, uri)
            putExtra(Intent.EXTRA_SUBJECT, "鱼探仪截图")
            putExtra(Intent.EXTRA_TEXT, "我的鱼探仪截图")
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        
        context.startActivity(Intent.createChooser(shareIntent, "分享截图"))
    }
} 