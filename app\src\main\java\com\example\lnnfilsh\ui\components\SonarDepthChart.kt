package com.example.lnnfilsh.ui.components

import android.util.Log
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.graphics.drawscope.translate

import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lnnfilsh.R
import com.example.lnnfilsh.model.FishData
import com.example.lnnfilsh.ui.theme.*
import kotlin.math.*

@Composable
fun SonarDepthChart(
    depthHistory: List<Float>,
    fishData: List<FishData>,
    ultrasonicData: List<Float> = emptyList(),
    modifier: Modifier = Modifier
) {
    val textMeasurer = rememberTextMeasurer()
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    // 预加载鱼的图片资源
    val xiaoBitmap = ImageBitmap.imageResource(R.drawable.xiao)
    val zhongBitmap = ImageBitmap.imageResource(R.drawable.zhong)
    val daBitmap = ImageBitmap.imageResource(R.drawable.da)
    
    // 雷达扫描动画 - 180°扫描（从左到右，再从右到左）
    val infiniteTransition = rememberInfiniteTransition(label = "radar_scan")
    val radarAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 180f,
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse // 来回扫描
        ),
        label = "radar_angle"
    )
    
    // 扫描线淡入淡出动画
    val scanAlpha by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scan_alpha"
    )
    
    // 获取屏幕尺寸
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }
    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }
    
    // 根据当前水深数据自动选择显示范围
    val currentMaxDepth = remember(depthHistory) {
        if (depthHistory.isEmpty()) {
            100f
        } else {
            val maxValue = depthHistory.maxOrNull() ?: 100f
            when {
                maxValue <= 100f -> 100f
                maxValue <= 150f -> 150f
                maxValue <= 200f -> 200f
                maxValue <= 300f -> 300f
                maxValue <= 450f -> 450f
                maxValue <= 500f -> 500f
                else -> 600f
            }
        }
    }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clipToBounds()
        ) {
            val width = size.width
            val height = size.height
            val centerX = width / 2f

            // 获取状态栏高度（大约24dp转换为像素）
            val statusBarHeight = 24.dp.toPx()

            // 将中心点设置在顶部状态栏下方，形成全屏半圆
            val centerY = statusBarHeight + 20.dp.toPx() // 状态栏下方留一点间距

            // 计算最大可用半径，确保半圆能填满整个屏幕
            val maxRadiusFromWidth = width / 2f
            val maxRadiusFromHeight = height - centerY
            val radius = minOf(maxRadiusFromWidth, maxRadiusFromHeight) * 0.95f // 留5%边距

            // 绘制全屏黑色背景
            drawRect(
                color = Color.Black,
                size = Size(width, height)
            )

            // 绘制声呐背景渐变 - 半圆形
            val sonarGradient = Brush.radialGradient(
                colors = listOf(
                    Color(0xFF001122).copy(alpha = 1f),   // 深海蓝中心
                    Color(0xFF002244).copy(alpha = 0.9f), // 中等蓝色
                    Color(0xFF003366).copy(alpha = 0.8f), // 外围蓝色
                    Color(0xFF004488).copy(alpha = 0.7f), // 边缘蓝色
                    Color.Black.copy(alpha = 0.5f)        // 外边缘渐变到黑色
                ),
                radius = radius,
                center = Offset(centerX, centerY)
            )

            // 绘制半圆形背景（向下的半圆）
            drawArc(
                brush = sonarGradient,
                startAngle = 0f,    // 从顶部开始
                sweepAngle = 180f,  // 向下扫描180度
                useCenter = true,
                topLeft = Offset(centerX - radius, centerY - radius),
                size = Size(radius * 2, radius * 2)
            )
            
            // 绘制同心半圆网格线（向下的半圆）
            val gridRings = 5
            for (i in 1..gridRings) {
                val ringRadius = radius * i / gridRings
                drawArc(
                    color = Color(0xFF00FF88).copy(alpha = 0.3f),
                    startAngle = 0f,    // 从顶部开始
                    sweepAngle = 180f,  // 向下180度
                    useCenter = false,
                    topLeft = Offset(centerX - ringRadius, centerY - ringRadius),
                    size = Size(ringRadius * 2, ringRadius * 2),
                    style = Stroke(width = 1f)
                )
                
                // 绘制深度标签
                if (i < gridRings) {
                    val depth = currentMaxDepth * i / gridRings
                    val depthText = String.format("%.1fm", depth / 100)
                    val textStyle = TextStyle(
                        fontSize = 12.sp,
                        color = Color(0xFF00FF88).copy(alpha = 0.8f),
                        fontWeight = FontWeight.Bold
                    )
                    val textSize = textMeasurer.measure(depthText, textStyle)
                    
                    drawText(
                        textMeasurer = textMeasurer,
                        text = depthText,
                        topLeft = Offset(
                            centerX + ringRadius - textSize.size.width / 2,
                            centerY - textSize.size.height / 2
                        ),
                        style = textStyle
                    )
                }
            }
            
            // 绘制雷达屏幕边框（向下的半圆）
            drawArc(
                color = Color(0xFF00FF88).copy(alpha = 0.6f),
                startAngle = 0f,    // 从顶部开始
                sweepAngle = 180f,  // 向下180度
                useCenter = false,
                topLeft = Offset(centerX - radius, centerY - radius),
                size = Size(radius * 2, radius * 2),
                style = Stroke(width = 3f)
            )

            // 绘制半圆分割线
            // 水平基线（加粗，作为水面线）- 现在在顶部
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = 0.8f),
                start = Offset(centerX - radius, centerY),
                end = Offset(centerX + radius, centerY),
                strokeWidth = 4f
            )

            // 中央垂直线（向下）
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = 0.5f),
                start = Offset(centerX, centerY),
                end = Offset(centerX, centerY + radius),
                strokeWidth = 2f
            )

            // 绘制角度标记线和标签（每30度一条）- 向下的角度
            for (angle in listOf(30f, 60f, 120f, 150f)) {
                val angleRad = angle * PI / 180f
                val startRadius = radius * 0.9f
                val endRadius = radius
                val labelRadius = radius * 1.05f

                // 绘制角度标记线
                drawLine(
                    color = Color(0xFF00FF88).copy(alpha = 0.4f),
                    start = Offset(
                        centerX + startRadius * cos(angleRad).toFloat(),
                        centerY + startRadius * sin(angleRad).toFloat()
                    ),
                    end = Offset(
                        centerX + endRadius * cos(angleRad).toFloat(),
                        centerY + endRadius * sin(angleRad).toFloat()
                    ),
                    strokeWidth = 2f
                )

                // 绘制角度标签（修正角度显示）
                val angleText = "${angle.toInt()}°"
                val textStyle = TextStyle(
                    fontSize = 10.sp,
                    color = Color(0xFF00FF88).copy(alpha = 0.7f),
                    fontWeight = FontWeight.Bold
                )
                val textSize = textMeasurer.measure(angleText, textStyle)

                drawText(
                    textMeasurer = textMeasurer,
                    text = angleText,
                    topLeft = Offset(
                        centerX + labelRadius * cos(angleRad).toFloat() - textSize.size.width / 2,
                        centerY + labelRadius * sin(angleRad).toFloat() - textSize.size.height / 2
                    ),
                    style = textStyle
                )
            }

            // 45度角线（左下）
            val angle45 = 45f * PI / 180f
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = 0.4f),
                start = Offset(centerX, centerY),
                end = Offset(
                    centerX - radius * cos(angle45).toFloat(),
                    centerY + radius * sin(angle45).toFloat()
                ),
                strokeWidth = 1.5f
            )
            // 45度角线（右下）
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = 0.4f),
                start = Offset(centerX, centerY),
                end = Offset(
                    centerX + radius * cos(angle45).toFloat(),
                    centerY + radius * sin(angle45).toFloat()
                ),
                strokeWidth = 1.5f
            )
            
            // 绘制雷达扫描线 - 180°扫描（向下）
            val scanLineAngle = radarAngle // 从0度开始向下扫描到180度

            // 绘制扫描线的发光效果（多层）
            val scanEndX = centerX + radius * cos(scanLineAngle * PI / 180f).toFloat()
            val scanEndY = centerY + radius * sin(scanLineAngle * PI / 180f).toFloat()

            // 外层发光
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = scanAlpha * 0.3f),
                start = Offset(centerX, centerY),
                end = Offset(scanEndX, scanEndY),
                strokeWidth = 8f
            )

            // 中层发光
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = scanAlpha * 0.6f),
                start = Offset(centerX, centerY),
                end = Offset(scanEndX, scanEndY),
                strokeWidth = 5f
            )

            // 主扫描线
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = scanAlpha),
                start = Offset(centerX, centerY),
                end = Offset(scanEndX, scanEndY),
                strokeWidth = 2f
            )

            // 绘制扫描扇形区域（增强效果）
            val scanGradient = Brush.sweepGradient(
                colors = listOf(
                    Color.Transparent,
                    Color(0xFF00FF88).copy(alpha = scanAlpha * 0.4f),
                    Color(0xFF00FF88).copy(alpha = scanAlpha * 0.2f),
                    Color(0xFF00FF88).copy(alpha = scanAlpha * 0.1f),
                    Color.Transparent
                ),
                center = Offset(centerX, centerY)
            )

            drawArc(
                brush = scanGradient,
                startAngle = scanLineAngle - 20f,
                sweepAngle = 40f,
                useCenter = true,
                topLeft = Offset(centerX - radius, centerY - radius),
                size = Size(radius * 2, radius * 2)
            )
            
            // 绘制水底轮廓数据
            if (depthHistory.isNotEmpty()) {
                drawSonarBottomContour(
                    depthHistory = depthHistory,
                    centerX = centerX,
                    centerY = centerY,
                    radius = radius,
                    maxDepth = currentMaxDepth,
                    textMeasurer = textMeasurer
                )
            }
            
            // 绘制鱼群数据
            fishData.forEach { fish ->
                drawSonarFish(
                    fish = fish,
                    centerX = centerX,
                    centerY = centerY,
                    radius = radius,
                    maxDepth = currentMaxDepth,
                    radarAngle = radarAngle,
                    scanAlpha = scanAlpha,
                    xiaoBitmap = xiaoBitmap,
                    zhongBitmap = zhongBitmap,
                    daBitmap = daBitmap
                )
            }
            
            // 绘制超声波数据点
            if (ultrasonicData.isNotEmpty()) {
                drawSonarUltrasonicData(
                    ultrasonicData = ultrasonicData,
                    centerX = centerX,
                    centerY = centerY,
                    radius = radius,
                    maxDepth = currentMaxDepth
                )
            }
            
            // 绘制中心点（声呐发射器）
            // 外层发光环
            drawCircle(
                color = Color(0xFF00FF88).copy(alpha = 0.3f),
                radius = 12f,
                center = Offset(centerX, centerY)
            )

            // 中层环
            drawCircle(
                color = Color(0xFF00FF88).copy(alpha = 0.6f),
                radius = 8f,
                center = Offset(centerX, centerY)
            )

            // 内核
            drawCircle(
                color = Color(0xFF00FF88),
                radius = 4f,
                center = Offset(centerX, centerY)
            )

            // 中心亮点
            drawCircle(
                color = Color.White,
                radius = 2f,
                center = Offset(centerX, centerY)
            )
        }
    }
}

// 绘制声呐模式的水底轮廓 - 180°半圆（向下）
private fun DrawScope.drawSonarBottomContour(
    depthHistory: List<Float>,
    centerX: Float,
    centerY: Float,
    radius: Float,
    maxDepth: Float,
    textMeasurer: androidx.compose.ui.text.TextMeasurer
) {
    val points = mutableListOf<Offset>()
    val angleStep = 180f / depthHistory.size // 180度分布

    depthHistory.forEachIndexed { index, depth ->
        val angle = (index * angleStep) * PI / 180f // 从0度开始向下扫描
        val depthRatio = (depth / maxDepth).coerceIn(0f, 1f)
        val pointRadius = radius * depthRatio

        val x = centerX + pointRadius * cos(angle).toFloat()
        val y = centerY + pointRadius * sin(angle).toFloat()
        points.add(Offset(x, y))
    }
    
    // 绘制轮廓线
    if (points.size >= 2) {
        val path = Path()
        path.moveTo(points.first().x, points.first().y)
        points.forEach { point ->
            path.lineTo(point.x, point.y)
        }
        path.close()
        
        // 绘制填充
        drawPath(
            path = path,
            color = Color(0xFFFFAA00).copy(alpha = 0.3f)
        )
        
        // 绘制轮廓线
        drawPath(
            path = path,
            color = Color(0xFFFFAA00).copy(alpha = 0.8f),
            style = Stroke(width = 2f)
        )
    }
}

// 绘制声呐模式的鱼群 - 使用图片显示
private fun DrawScope.drawSonarFish(
    fish: FishData,
    centerX: Float,
    centerY: Float,
    radius: Float,
    maxDepth: Float,
    radarAngle: Float,
    scanAlpha: Float,
    xiaoBitmap: ImageBitmap,
    zhongBitmap: ImageBitmap,
    daBitmap: ImageBitmap
) {
    val depthRatio = (fish.depth / maxDepth).coerceIn(0f, 1f)
    val fishRadius = radius * depthRatio

    // 将鱼的x坐标转换为180°角度范围（向下）
    val angle = ((fish.x / 100f) * 180f) * PI / 180f // 从0度到180度分布

    val x = centerX + fishRadius * cos(angle).toFloat()
    val y = centerY + fishRadius * sin(angle).toFloat()

    // 根据雷达扫描位置调整鱼的可见度
    val fishAngleDeg = (fish.x / 100f) * 180f // 转换为180度范围
    val angleDiff = abs(fishAngleDeg - radarAngle)
    val visibility = if (angleDiff < 20f) 1f else 0.4f // 扫描到的鱼完全可见，其他半透明
    
    // 根据鱼的大小选择图片和尺寸
    val (fishBitmap, fishSize) = when (fish.size) {
        1 -> Pair(xiaoBitmap, 24f) // 小鱼使用xiao.png
        2 -> Pair(zhongBitmap, 32f) // 中鱼使用zhong.png
        3 -> Pair(daBitmap, 40f) // 大鱼使用da.png
        else -> Pair(xiaoBitmap, 24f)
    }

    // 绘制鱼的图片
    drawImage(
        image = fishBitmap,
        dstOffset = IntOffset((x - fishSize / 2).toInt(), (y - fishSize / 2).toInt()),
        dstSize = IntSize(fishSize.toInt(), fishSize.toInt()),
        alpha = visibility,
        colorFilter = ColorFilter.tint(
            Color(0xFF00FF88).copy(alpha = visibility), // 声呐绿色调
            BlendMode.Modulate
        )
    )

    // 绘制鱼的回波环（扫描到时显示）
    if (visibility > 0.8f) {
        drawCircle(
            color = Color(0xFF00FF88).copy(alpha = visibility * 0.5f),
            radius = fishSize * 0.8f,
            center = Offset(x, y),
            style = Stroke(width = 2f)
        )

        // 绘制外层回波环
        drawCircle(
            color = Color(0xFF00FF88).copy(alpha = visibility * 0.3f),
            radius = fishSize * 1.2f,
            center = Offset(x, y),
            style = Stroke(width = 1f)
        )
    }
}

// 绘制声呐模式的超声波数据 - 180°分布（向下）
private fun DrawScope.drawSonarUltrasonicData(
    ultrasonicData: List<Float>,
    centerX: Float,
    centerY: Float,
    radius: Float,
    maxDepth: Float
) {
    ultrasonicData.forEachIndexed { index, depth ->
        val angle = ((index / ultrasonicData.size.toFloat()) * 180f) * PI / 180f // 从0度到180度分布
        val depthRatio = (depth / maxDepth).coerceIn(0f, 1f)
        val pointRadius = radius * depthRatio

        val x = centerX + pointRadius * cos(angle).toFloat()
        val y = centerY + pointRadius * sin(angle).toFloat()

        drawCircle(
            color = Color(0xFF00FFFF).copy(alpha = 0.8f),
            radius = 3f,
            center = Offset(x, y)
        )

        // 绘制数据点的脉冲效果
        drawCircle(
            color = Color(0xFF00FFFF).copy(alpha = 0.3f),
            radius = 6f,
            center = Offset(x, y),
            style = Stroke(width = 1f)
        )
    }
}
