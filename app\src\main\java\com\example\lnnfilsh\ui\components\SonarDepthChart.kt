package com.example.lnnfilsh.ui.components

import android.util.Log
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lnnfilsh.R
import com.example.lnnfilsh.model.FishData
import com.example.lnnfilsh.ui.theme.*
import kotlin.math.*

@Composable
fun SonarDepthChart(
    depthHistory: List<Float>,
    fishData: List<FishData>,
    ultrasonicData: List<Float> = emptyList(),
    modifier: Modifier = Modifier
) {
    val textMeasurer = rememberTextMeasurer()
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    
    // 雷达扫描动画 - 180°扫描（从左到右，再从右到左）
    val infiniteTransition = rememberInfiniteTransition(label = "radar_scan")
    val radarAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 180f,
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse // 来回扫描
        ),
        label = "radar_angle"
    )
    
    // 扫描线淡入淡出动画
    val scanAlpha by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scan_alpha"
    )
    
    // 获取屏幕尺寸
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }
    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }
    
    // 根据当前水深数据自动选择显示范围
    val currentMaxDepth = remember(depthHistory) {
        if (depthHistory.isEmpty()) {
            100f
        } else {
            val maxValue = depthHistory.maxOrNull() ?: 100f
            when {
                maxValue <= 100f -> 100f
                maxValue <= 150f -> 150f
                maxValue <= 200f -> 200f
                maxValue <= 300f -> 300f
                maxValue <= 450f -> 450f
                maxValue <= 500f -> 500f
                else -> 600f
            }
        }
    }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clipToBounds()
        ) {
            val width = size.width
            val height = size.height
            val centerX = width / 2f
            val centerY = height * 0.85f // 将中心点移到底部，形成半圆
            val radius = minOf(width, height * 0.85f) / 2f * 0.9f
            
            // 绘制声呐背景渐变
            val sonarGradient = Brush.radialGradient(
                colors = listOf(
                    Color(0xFF001122).copy(alpha = 0.9f), // 深海蓝中心
                    Color(0xFF002244).copy(alpha = 0.8f), // 中等蓝色
                    Color(0xFF003366).copy(alpha = 0.7f), // 外围蓝色
                    Color(0xFF004488).copy(alpha = 0.6f)  // 边缘蓝色
                ),
                radius = radius
            )
            drawCircle(
                brush = sonarGradient,
                radius = radius,
                center = Offset(centerX, centerY)
            )
            
            // 绘制同心圆网格线
            val gridRings = 5
            for (i in 1..gridRings) {
                val ringRadius = radius * i / gridRings
                drawCircle(
                    color = Color(0xFF00FF88).copy(alpha = 0.3f),
                    radius = ringRadius,
                    center = Offset(centerX, centerY),
                    style = Stroke(width = 1f)
                )
                
                // 绘制深度标签
                if (i < gridRings) {
                    val depth = currentMaxDepth * i / gridRings
                    val depthText = String.format("%.1fm", depth / 100)
                    val textStyle = TextStyle(
                        fontSize = 12.sp,
                        color = Color(0xFF00FF88).copy(alpha = 0.8f),
                        fontWeight = FontWeight.Bold
                    )
                    val textSize = textMeasurer.measure(depthText, textStyle)
                    
                    drawText(
                        textMeasurer = textMeasurer,
                        text = depthText,
                        topLeft = Offset(
                            centerX + ringRadius - textSize.size.width / 2,
                            centerY - textSize.size.height / 2
                        ),
                        style = textStyle
                    )
                }
            }
            
            // 绘制十字线
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = 0.4f),
                start = Offset(centerX - radius, centerY),
                end = Offset(centerX + radius, centerY),
                strokeWidth = 1f
            )
            drawLine(
                color = Color(0xFF00FF88).copy(alpha = 0.4f),
                start = Offset(centerX, centerY - radius),
                end = Offset(centerX, centerY + radius),
                strokeWidth = 1f
            )
            
            // 绘制雷达扫描线
            rotate(radarAngle, pivot = Offset(centerX, centerY)) {
                val scanGradient = Brush.sweepGradient(
                    colors = listOf(
                        Color.Transparent,
                        Color(0xFF00FF88).copy(alpha = scanAlpha * 0.8f),
                        Color(0xFF00FF88).copy(alpha = scanAlpha * 0.4f),
                        Color.Transparent
                    ),
                    center = Offset(centerX, centerY)
                )
                
                drawArc(
                    brush = scanGradient,
                    startAngle = -10f,
                    sweepAngle = 20f,
                    useCenter = true,
                    topLeft = Offset(centerX - radius, centerY - radius),
                    size = Size(radius * 2, radius * 2)
                )
            }
            
            // 绘制水底轮廓数据
            if (depthHistory.isNotEmpty()) {
                drawSonarBottomContour(
                    depthHistory = depthHistory,
                    centerX = centerX,
                    centerY = centerY,
                    radius = radius,
                    maxDepth = currentMaxDepth,
                    textMeasurer = textMeasurer
                )
            }
            
            // 绘制鱼群数据
            fishData.forEach { fish ->
                drawSonarFish(
                    fish = fish,
                    centerX = centerX,
                    centerY = centerY,
                    radius = radius,
                    maxDepth = currentMaxDepth,
                    radarAngle = radarAngle,
                    scanAlpha = scanAlpha
                )
            }
            
            // 绘制超声波数据点
            if (ultrasonicData.isNotEmpty()) {
                drawSonarUltrasonicData(
                    ultrasonicData = ultrasonicData,
                    centerX = centerX,
                    centerY = centerY,
                    radius = radius,
                    maxDepth = currentMaxDepth
                )
            }
            
            // 绘制中心点
            drawCircle(
                color = Color(0xFF00FF88),
                radius = 4f,
                center = Offset(centerX, centerY)
            )
        }
    }
}

// 绘制声呐模式的水底轮廓
private fun DrawScope.drawSonarBottomContour(
    depthHistory: List<Float>,
    centerX: Float,
    centerY: Float,
    radius: Float,
    maxDepth: Float,
    textMeasurer: androidx.compose.ui.text.TextMeasurer
) {
    val points = mutableListOf<Offset>()
    val angleStep = 360f / depthHistory.size
    
    depthHistory.forEachIndexed { index, depth ->
        val angle = index * angleStep * PI / 180f
        val depthRatio = (depth / maxDepth).coerceIn(0f, 1f)
        val pointRadius = radius * depthRatio
        
        val x = centerX + pointRadius * cos(angle).toFloat()
        val y = centerY + pointRadius * sin(angle).toFloat()
        points.add(Offset(x, y))
    }
    
    // 绘制轮廓线
    if (points.size >= 2) {
        val path = Path()
        path.moveTo(points.first().x, points.first().y)
        points.forEach { point ->
            path.lineTo(point.x, point.y)
        }
        path.close()
        
        // 绘制填充
        drawPath(
            path = path,
            color = Color(0xFFFFAA00).copy(alpha = 0.3f)
        )
        
        // 绘制轮廓线
        drawPath(
            path = path,
            color = Color(0xFFFFAA00).copy(alpha = 0.8f),
            style = Stroke(width = 2f)
        )
    }
}

// 绘制声呐模式的鱼群
private fun DrawScope.drawSonarFish(
    fish: FishData,
    centerX: Float,
    centerY: Float,
    radius: Float,
    maxDepth: Float,
    radarAngle: Float,
    scanAlpha: Float
) {
    val depthRatio = (fish.depth / maxDepth).coerceIn(0f, 1f)
    val fishRadius = radius * depthRatio
    
    // 将鱼的x坐标转换为角度
    val angle = (fish.x / 100f) * 360f * PI / 180f
    
    val x = centerX + fishRadius * cos(angle).toFloat()
    val y = centerY + fishRadius * sin(angle).toFloat()
    
    // 根据雷达扫描位置调整鱼的可见度
    val fishAngleDeg = (fish.x / 100f) * 360f
    val angleDiff = abs(fishAngleDeg - radarAngle)
    val normalizedDiff = minOf(angleDiff, 360f - angleDiff)
    val visibility = if (normalizedDiff < 30f) scanAlpha else 0.3f
    
    // 根据鱼的大小选择颜色和大小
    val fishColor = when (fish.size) {
        1 -> Color(0xFF00FFFF).copy(alpha = visibility) // 青色小鱼
        2 -> Color(0xFF00FF00).copy(alpha = visibility) // 绿色中鱼
        3 -> Color(0xFFFFFF00).copy(alpha = visibility) // 黄色大鱼
        else -> Color(0xFF00FFFF).copy(alpha = visibility)
    }
    
    val fishSize = when (fish.size) {
        1 -> 3f
        2 -> 5f
        3 -> 8f
        else -> 3f
    }
    
    // 绘制鱼点
    drawCircle(
        color = fishColor,
        radius = fishSize,
        center = Offset(x, y)
    )
    
    // 绘制鱼的回波环
    if (visibility > 0.5f) {
        drawCircle(
            color = fishColor.copy(alpha = visibility * 0.3f),
            radius = fishSize * 2f,
            center = Offset(x, y),
            style = Stroke(width = 1f)
        )
    }
}

// 绘制声呐模式的超声波数据
private fun DrawScope.drawSonarUltrasonicData(
    ultrasonicData: List<Float>,
    centerX: Float,
    centerY: Float,
    radius: Float,
    maxDepth: Float
) {
    ultrasonicData.forEachIndexed { index, depth ->
        val angle = (index / ultrasonicData.size.toFloat()) * 360f * PI / 180f
        val depthRatio = (depth / maxDepth).coerceIn(0f, 1f)
        val pointRadius = radius * depthRatio
        
        val x = centerX + pointRadius * cos(angle).toFloat()
        val y = centerY + pointRadius * sin(angle).toFloat()
        
        drawCircle(
            color = Color(0xFF00FFFF).copy(alpha = 0.8f),
            radius = 2f,
            center = Offset(x, y)
        )
    }
}
