  Activity android.app  Application android.app  
Configuration android.app.Activity  	Exception android.app.Activity  	Lifecycle android.app.Activity  
LnnfilshTheme android.app.Activity  Log android.app.Activity  
MainScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  TAG android.app.Activity  Thread android.app.Activity  WindowCompat android.app.Activity  WindowInsetsCompat android.app.Activity  WindowInsetsControllerCompat android.app.Activity  
WindowManager android.app.Activity  fillMaxSize android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  repeatOnLifecycle android.app.Activity  
setContent android.app.Activity  window android.app.Activity  getSharedPreferences android.app.Application  
ContentValues android.content  Context android.content  Intent android.content  insert android.content.ContentResolver  openOutputStream android.content.ContentResolver  Environment android.content.ContentValues  
MediaStore android.content.ContentValues  apply android.content.ContentValues  put android.content.ContentValues  
Configuration android.content.Context  	Exception android.content.Context  	Lifecycle android.content.Context  
LnnfilshTheme android.content.Context  Log android.content.Context  MODE_PRIVATE android.content.Context  
MainScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  TAG android.content.Context  Thread android.content.Context  WindowCompat android.content.Context  WindowInsetsCompat android.content.Context  WindowInsetsControllerCompat android.content.Context  
WindowManager android.content.Context  applicationContext android.content.Context  contentResolver android.content.Context  fillMaxSize android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  packageName android.content.Context  repeatOnLifecycle android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  
Configuration android.content.ContextWrapper  	Exception android.content.ContextWrapper  	Lifecycle android.content.ContextWrapper  
LnnfilshTheme android.content.ContextWrapper  Log android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  TAG android.content.ContextWrapper  Thread android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  WindowInsetsCompat android.content.ContextWrapper  WindowInsetsControllerCompat android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  repeatOnLifecycle android.content.ContextWrapper  
setContent android.content.ContextWrapper  ACTION_SEND android.content.Intent  EXTRA_STREAM android.content.Intent  
EXTRA_SUBJECT android.content.Intent  
EXTRA_TEXT android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  Intent android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  
createChooser android.content.Intent  putExtra android.content.Intent  type android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getInt !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  ActivityInfo android.content.pm  
Configuration android.content.res  ORIENTATION_LANDSCAPE !android.content.res.Configuration  orientation !android.content.res.Configuration  screenHeightDp !android.content.res.Configuration  
screenWidthDp !android.content.res.Configuration  Bitmap android.graphics  Canvas android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  compress android.graphics.Bitmap  createBitmap android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  MediaPlayer 
android.media  OnCompletionListener android.media.MediaPlayer  create android.media.MediaPlayer  release android.media.MediaPlayer  setOnCompletionListener android.media.MediaPlayer  start android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  Uri android.net  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  VibrationEffect 
android.os  Vibrator 
android.os  VibratorManager 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  DIRECTORY_PICTURES android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  postDelayed android.os.Handler  
getMainLooper android.os.Looper  
MediaStore android.provider  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  View android.view  Window android.view  
WindowManager android.view  
Configuration  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  	Lifecycle  android.view.ContextThemeWrapper  
LnnfilshTheme  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Thread  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  WindowInsetsCompat  android.view.ContextThemeWrapper  WindowInsetsControllerCompat  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  repeatOnLifecycle  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  draw android.view.View  height android.view.View  isInEditMode android.view.View  let android.view.View  width android.view.View  addFlags android.view.Window  
clearFlags android.view.Window  	decorView android.view.Window  navigationBarColor android.view.Window  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  Toast android.widget  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  
Configuration #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  	Lifecycle #androidx.activity.ComponentActivity  
LnnfilshTheme #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  Thread #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  WindowInsetsCompat #androidx.activity.ComponentActivity  WindowInsetsControllerCompat #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  onConfigurationChanged #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  repeatOnLifecycle #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  	BlendMode androidx.compose.animation.core  Brush androidx.compose.animation.core  Canvas androidx.compose.animation.core  Color androidx.compose.animation.core  ColorFilter androidx.compose.animation.core  
Composable androidx.compose.animation.core  	DrawScope androidx.compose.animation.core  Easing androidx.compose.animation.core  FastOutSlowInEasing androidx.compose.animation.core  FishData androidx.compose.animation.core  Float androidx.compose.animation.core  
FontWeight androidx.compose.animation.core  ImageBitmap androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  	IntOffset androidx.compose.animation.core  IntSize androidx.compose.animation.core  LinearEasing androidx.compose.animation.core  List androidx.compose.animation.core  Modifier androidx.compose.animation.core  Offset androidx.compose.animation.core  PI androidx.compose.animation.core  Pair androidx.compose.animation.core  Path androidx.compose.animation.core  R androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  Size androidx.compose.animation.core  Stroke androidx.compose.animation.core  	TextStyle androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  abs androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloat androidx.compose.animation.core  clipToBounds androidx.compose.animation.core  coerceIn androidx.compose.animation.core  cos androidx.compose.animation.core  	emptyList androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  first androidx.compose.animation.core  forEach androidx.compose.animation.core  forEachIndexed androidx.compose.animation.core  getValue androidx.compose.animation.core  
imageResource androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  
isNotEmpty androidx.compose.animation.core  listOf androidx.compose.animation.core  	maxOrNull androidx.compose.animation.core  minOf androidx.compose.animation.core  
mutableListOf androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  radialGradient androidx.compose.animation.core  remember androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  sin androidx.compose.animation.core  
sweepGradient androidx.compose.animation.core  tint androidx.compose.animation.core  tween androidx.compose.animation.core  with androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  Reverse *androidx.compose.animation.core.RepeatMode  compose (androidx.compose.animation.core.androidx  ui 0androidx.compose.animation.core.androidx.compose  text 3androidx.compose.animation.core.androidx.compose.ui  TextMeasurer 8androidx.compose.animation.core.androidx.compose.ui.text  Canvas androidx.compose.foundation  Image androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  detectTransformGestures $androidx.compose.foundation.gestures  detectVerticalDragGestures $androidx.compose.foundation.gestures  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  WindowInsets "androidx.compose.foundation.layout  asPaddingValues "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  navigationBars "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
statusBars "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
AccentBlue +androidx.compose.foundation.layout.BoxScope  Add +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Canvas +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
DepthChart +androidx.compose.foundation.layout.BoxScope  DepthChartWithLabels +androidx.compose.foundation.layout.BoxScope  
FishIconWhite +androidx.compose.foundation.layout.BoxScope  FishIconYellow +androidx.compose.foundation.layout.BoxScope  FloatingActionButton +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Handler +androidx.compose.foundation.layout.BoxScope  HorizontalDivider +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  	Lightbulb +androidx.compose.foundation.layout.BoxScope  Log +androidx.compose.foundation.layout.BoxScope  Looper +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  PI +androidx.compose.foundation.layout.BoxScope  Path +androidx.compose.foundation.layout.BoxScope  
PathEffect +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  Rect +androidx.compose.foundation.layout.BoxScope  Refresh +androidx.compose.foundation.layout.BoxScope  RiverbedDarkOrange +androidx.compose.foundation.layout.BoxScope  RiverbedDeepOrange +androidx.compose.foundation.layout.BoxScope  RiverbedMidOrange +androidx.compose.foundation.layout.BoxScope  RiverbedSoilBrown +androidx.compose.foundation.layout.BoxScope  RiverbedSurfaceGold +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  ScreenshotUtil +androidx.compose.foundation.layout.BoxScope  SecondaryIndicator +androidx.compose.foundation.layout.BoxScope  Settings +androidx.compose.foundation.layout.BoxScope  Size +androidx.compose.foundation.layout.BoxScope  Slider +androidx.compose.foundation.layout.BoxScope  SliderDefaults +androidx.compose.foundation.layout.BoxScope  SonarDepthChart +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  String +androidx.compose.foundation.layout.BoxScope  Stroke +androidx.compose.foundation.layout.BoxScope  	StrokeCap +androidx.compose.foundation.layout.BoxScope  
StrokeJoin +androidx.compose.foundation.layout.BoxScope  Switch +androidx.compose.foundation.layout.BoxScope  SwitchDefaults +androidx.compose.foundation.layout.BoxScope  Tab +androidx.compose.foundation.layout.BoxScope  TabRow +androidx.compose.foundation.layout.BoxScope  TabRowDefaults +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  	TextStyle +androidx.compose.foundation.layout.BoxScope  Toast +androidx.compose.foundation.layout.BoxScope  Unit +androidx.compose.foundation.layout.BoxScope  WaterAbyssBlue +androidx.compose.foundation.layout.BoxScope  
WaterDeepBlue +androidx.compose.foundation.layout.BoxScope  
WaterDeepNavy +androidx.compose.foundation.layout.BoxScope  WaterDodgerBlue +androidx.compose.foundation.layout.BoxScope  WaterSkyBlue +androidx.compose.foundation.layout.BoxScope  WaterSteelBlue +androidx.compose.foundation.layout.BoxScope  WifiOff +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  alpha +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  
cardElevation +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  clipToBounds +androidx.compose.foundation.layout.BoxScope  coerceIn +androidx.compose.foundation.layout.BoxScope  colors +androidx.compose.foundation.layout.BoxScope  cos +androidx.compose.foundation.layout.BoxScope  dashPathEffect +androidx.compose.foundation.layout.BoxScope  detectVerticalDragGestures +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  drawSonarBottomContour +androidx.compose.foundation.layout.BoxScope  
drawSonarFish +androidx.compose.foundation.layout.BoxScope  drawSonarUltrasonicData +androidx.compose.foundation.layout.BoxScope  drawText +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  first +androidx.compose.foundation.layout.BoxScope  floatArrayOf +androidx.compose.foundation.layout.BoxScope  forEachIndexed +androidx.compose.foundation.layout.BoxScope  format +androidx.compose.foundation.layout.BoxScope  	getOrNull +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  invoke +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  kotlin +androidx.compose.foundation.layout.BoxScope  last +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  min +androidx.compose.foundation.layout.BoxScope  minOf +androidx.compose.foundation.layout.BoxScope  minOfOrNull +androidx.compose.foundation.layout.BoxScope  
mutableListOf +androidx.compose.foundation.layout.BoxScope  	nextFloat +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  pointerInput +androidx.compose.foundation.layout.BoxScope  radialGradient +androidx.compose.foundation.layout.BoxScope  rangeTo +androidx.compose.foundation.layout.BoxScope  rememberScrollState +androidx.compose.foundation.layout.BoxScope  repeat +androidx.compose.foundation.layout.BoxScope  sin +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  step +androidx.compose.foundation.layout.BoxScope  
sweepGradient +androidx.compose.foundation.layout.BoxScope  tabIndicatorOffset +androidx.compose.foundation.layout.BoxScope  takeLast +androidx.compose.foundation.layout.BoxScope  takeScreenshotAndShare +androidx.compose.foundation.layout.BoxScope  until +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  verticalScroll +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  
AccentBlue .androidx.compose.foundation.layout.ColumnScope  AccentGreen .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  BrightAccent .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleIconButton .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ConnectedGreen .androidx.compose.foundation.layout.ColumnScope  DepthChartWithLabels .androidx.compose.foundation.layout.ColumnScope  DisconnectedRed .androidx.compose.foundation.layout.ColumnScope  FishIconYellow .androidx.compose.foundation.layout.ColumnScope  FloatingActionButton .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Handler .androidx.compose.foundation.layout.ColumnScope  HorizontalDivider .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	Lightbulb .androidx.compose.foundation.layout.ColumnScope  Looper .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  R .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  ScreenshotUtil .androidx.compose.foundation.layout.ColumnScope  SecondaryIndicator .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  Slider .androidx.compose.foundation.layout.ColumnScope  SliderDefaults .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Stop .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  SwitchDefaults .androidx.compose.foundation.layout.ColumnScope  Tab .androidx.compose.foundation.layout.ColumnScope  TabRow .androidx.compose.foundation.layout.ColumnScope  TabRowDefaults .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  Unit .androidx.compose.foundation.layout.ColumnScope  
WaterDeepBlue .androidx.compose.foundation.layout.ColumnScope  WifiOff .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  alpha .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  detectVerticalDragGestures .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  offset .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  painterResource .androidx.compose.foundation.layout.ColumnScope  pointerInput .androidx.compose.foundation.layout.ColumnScope  rangeTo .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  tabIndicatorOffset .androidx.compose.foundation.layout.ColumnScope  takeScreenshotAndShare .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  calculateBottomPadding 0androidx.compose.foundation.layout.PaddingValues  
AccentBlue +androidx.compose.foundation.layout.RowScope  AccentGreen +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  BrightAccent +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  CircleIconButton +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  ConnectedGreen +androidx.compose.foundation.layout.RowScope  ControlButtonBg +androidx.compose.foundation.layout.RowScope  ControlButtonTextColor +androidx.compose.foundation.layout.RowScope  DisconnectedRed +androidx.compose.foundation.layout.RowScope  FishIconYellow +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Handler +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  	Lightbulb +androidx.compose.foundation.layout.RowScope  Looper +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  R +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  ScreenshotUtil +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Stop +androidx.compose.foundation.layout.RowScope  String +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  SwitchDefaults +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  WifiOff +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  painterResource +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  takeScreenshotAndShare +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  	Companion /androidx.compose.foundation.layout.WindowInsets  asPaddingValues /androidx.compose.foundation.layout.WindowInsets  navigationBars /androidx.compose.foundation.layout.WindowInsets  
statusBars /androidx.compose.foundation.layout.WindowInsets  navigationBars 9androidx.compose.foundation.layout.WindowInsets.Companion  
statusBars 9androidx.compose.foundation.layout.WindowInsets.Companion  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Outlined %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	Lightbulb ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Stop ,androidx.compose.material.icons.Icons.Filled  WifiOff ,androidx.compose.material.icons.Icons.Filled  	Lightbulb .androidx.compose.material.icons.Icons.Outlined  	PlayArrow .androidx.compose.material.icons.Icons.Outlined  Stop .androidx.compose.material.icons.Icons.Outlined  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  ArrowForward &androidx.compose.material.icons.filled  	ArrowLeft &androidx.compose.material.icons.filled  
ArrowRight &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  	Lightbulb &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  Stop &androidx.compose.material.icons.filled  WifiOff &androidx.compose.material.icons.filled  	Lightbulb (androidx.compose.material.icons.outlined  	PlayArrow (androidx.compose.material.icons.outlined  Stop (androidx.compose.material.icons.outlined  Wifi (androidx.compose.material.icons.outlined  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  ColorScheme androidx.compose.material3  Divider androidx.compose.material3  FilledIconButton androidx.compose.material3  FloatingActionButton androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  
MaterialTheme androidx.compose.material3  Slider androidx.compose.material3  SliderColors androidx.compose.material3  SliderDefaults androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  SwitchColors androidx.compose.material3  SwitchDefaults androidx.compose.material3  Tab androidx.compose.material3  TabPosition androidx.compose.material3  TabRow androidx.compose.material3  TabRowDefaults androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  colors )androidx.compose.material3.SliderDefaults  colors )androidx.compose.material3.SwitchDefaults  SecondaryIndicator )androidx.compose.material3.TabRowDefaults  tabIndicatorOffset )androidx.compose.material3.TabRowDefaults  	bodyLarge %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  	BlendMode androidx.compose.runtime  Brush androidx.compose.runtime  Canvas androidx.compose.runtime  Color androidx.compose.runtime  ColorFilter androidx.compose.runtime  
Composable androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  	DrawScope androidx.compose.runtime  FastOutSlowInEasing androidx.compose.runtime  FishData androidx.compose.runtime  Float androidx.compose.runtime  
FontWeight androidx.compose.runtime  ImageBitmap androidx.compose.runtime  	IntOffset androidx.compose.runtime  IntSize androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  LinearEasing androidx.compose.runtime  List androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  PI androidx.compose.runtime  Pair androidx.compose.runtime  Path androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  R androidx.compose.runtime  
RepeatMode androidx.compose.runtime  
SideEffect androidx.compose.runtime  Size androidx.compose.runtime  State androidx.compose.runtime  Stroke androidx.compose.runtime  	TextStyle androidx.compose.runtime  abs androidx.compose.runtime  androidx androidx.compose.runtime  animateFloat androidx.compose.runtime  clipToBounds androidx.compose.runtime  coerceIn androidx.compose.runtime  collectAsState androidx.compose.runtime  cos androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  first androidx.compose.runtime  forEach androidx.compose.runtime  forEachIndexed androidx.compose.runtime  getValue androidx.compose.runtime  
imageResource androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  listOf androidx.compose.runtime  	maxOrNull androidx.compose.runtime  minOf androidx.compose.runtime  mutableFloatStateOf androidx.compose.runtime  
mutableListOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  provideDelegate androidx.compose.runtime  radialGradient androidx.compose.runtime  remember androidx.compose.runtime  rememberInfiniteTransition androidx.compose.runtime  setValue androidx.compose.runtime  sin androidx.compose.runtime  
sweepGradient androidx.compose.runtime  tint androidx.compose.runtime  tween androidx.compose.runtime  with androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  	onDispose .androidx.compose.runtime.DisposableEffectScope  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  text ,androidx.compose.runtime.androidx.compose.ui  TextMeasurer 1androidx.compose.runtime.androidx.compose.ui.text  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Start androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  Start 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  alpha androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  clipToBounds androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  tabIndicatorOffset androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  offset &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  tabIndicatorOffset &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  alpha androidx.compose.ui.draw  clip androidx.compose.ui.draw  clipToBounds androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Rect androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  	BlendMode androidx.compose.ui.graphics  Brush androidx.compose.ui.graphics  Canvas androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  ColorFilter androidx.compose.ui.graphics  
Composable androidx.compose.ui.graphics  	DrawScope androidx.compose.ui.graphics  FastOutSlowInEasing androidx.compose.ui.graphics  FishData androidx.compose.ui.graphics  Float androidx.compose.ui.graphics  
FontWeight androidx.compose.ui.graphics  ImageBitmap androidx.compose.ui.graphics  	IntOffset androidx.compose.ui.graphics  IntSize androidx.compose.ui.graphics  LinearEasing androidx.compose.ui.graphics  List androidx.compose.ui.graphics  Modifier androidx.compose.ui.graphics  Offset androidx.compose.ui.graphics  PI androidx.compose.ui.graphics  Pair androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  
PathEffect androidx.compose.ui.graphics  R androidx.compose.ui.graphics  
RepeatMode androidx.compose.ui.graphics  Size androidx.compose.ui.graphics  Stroke androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  
StrokeJoin androidx.compose.ui.graphics  	TextStyle androidx.compose.ui.graphics  abs androidx.compose.ui.graphics  androidx androidx.compose.ui.graphics  animateFloat androidx.compose.ui.graphics  clipToBounds androidx.compose.ui.graphics  coerceIn androidx.compose.ui.graphics  cos androidx.compose.ui.graphics  	emptyList androidx.compose.ui.graphics  fillMaxSize androidx.compose.ui.graphics  first androidx.compose.ui.graphics  forEach androidx.compose.ui.graphics  forEachIndexed androidx.compose.ui.graphics  getValue androidx.compose.ui.graphics  
imageResource androidx.compose.ui.graphics  infiniteRepeatable androidx.compose.ui.graphics  
isNotEmpty androidx.compose.ui.graphics  listOf androidx.compose.ui.graphics  	maxOrNull androidx.compose.ui.graphics  minOf androidx.compose.ui.graphics  
mutableListOf androidx.compose.ui.graphics  provideDelegate androidx.compose.ui.graphics  radialGradient androidx.compose.ui.graphics  remember androidx.compose.ui.graphics  rememberInfiniteTransition androidx.compose.ui.graphics  sin androidx.compose.ui.graphics  
sweepGradient androidx.compose.ui.graphics  tint androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  tween androidx.compose.ui.graphics  with androidx.compose.ui.graphics  	Companion &androidx.compose.ui.graphics.BlendMode  Modulate &androidx.compose.ui.graphics.BlendMode  Modulate 0androidx.compose.ui.graphics.BlendMode.Companion  	Companion "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  
sweepGradient "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  
sweepGradient ,androidx.compose.ui.graphics.Brush.Companion  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  DarkGray "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  DarkGray ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  	Companion (androidx.compose.ui.graphics.ColorFilter  tint (androidx.compose.ui.graphics.ColorFilter  tint 2androidx.compose.ui.graphics.ColorFilter.Companion  	Companion (androidx.compose.ui.graphics.ImageBitmap  
imageResource (androidx.compose.ui.graphics.ImageBitmap  
imageResource 2androidx.compose.ui.graphics.ImageBitmap.Companion  Rect !androidx.compose.ui.graphics.Path  addOval !androidx.compose.ui.graphics.Path  apply !androidx.compose.ui.graphics.Path  close !androidx.compose.ui.graphics.Path  lineTo !androidx.compose.ui.graphics.Path  moveTo !androidx.compose.ui.graphics.Path  quadraticBezierTo !androidx.compose.ui.graphics.Path  	Companion 'androidx.compose.ui.graphics.PathEffect  dashPathEffect 'androidx.compose.ui.graphics.PathEffect  dashPathEffect 1androidx.compose.ui.graphics.PathEffect.Companion  	Companion &androidx.compose.ui.graphics.StrokeCap  Round &androidx.compose.ui.graphics.StrokeCap  Round 0androidx.compose.ui.graphics.StrokeCap.Companion  	Companion 'androidx.compose.ui.graphics.StrokeJoin  Round 'androidx.compose.ui.graphics.StrokeJoin  Round 1androidx.compose.ui.graphics.StrokeJoin.Companion  compose %androidx.compose.ui.graphics.androidx  ui -androidx.compose.ui.graphics.androidx.compose  text 0androidx.compose.ui.graphics.androidx.compose.ui  TextMeasurer 5androidx.compose.ui.graphics.androidx.compose.ui.text  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  rotate &androidx.compose.ui.graphics.drawscope  	translate &androidx.compose.ui.graphics.drawscope  	BlendMode 0androidx.compose.ui.graphics.drawscope.DrawScope  Brush 0androidx.compose.ui.graphics.drawscope.DrawScope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  ColorFilter 0androidx.compose.ui.graphics.drawscope.DrawScope  
FishIconWhite 0androidx.compose.ui.graphics.drawscope.DrawScope  
FontWeight 0androidx.compose.ui.graphics.drawscope.DrawScope  	IntOffset 0androidx.compose.ui.graphics.drawscope.DrawScope  IntSize 0androidx.compose.ui.graphics.drawscope.DrawScope  Log 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  PI 0androidx.compose.ui.graphics.drawscope.DrawScope  Pair 0androidx.compose.ui.graphics.drawscope.DrawScope  Path 0androidx.compose.ui.graphics.drawscope.DrawScope  
PathEffect 0androidx.compose.ui.graphics.drawscope.DrawScope  Rect 0androidx.compose.ui.graphics.drawscope.DrawScope  RiverbedDarkOrange 0androidx.compose.ui.graphics.drawscope.DrawScope  RiverbedDeepOrange 0androidx.compose.ui.graphics.drawscope.DrawScope  RiverbedMidOrange 0androidx.compose.ui.graphics.drawscope.DrawScope  RiverbedSoilBrown 0androidx.compose.ui.graphics.drawscope.DrawScope  RiverbedSurfaceGold 0androidx.compose.ui.graphics.drawscope.DrawScope  Size 0androidx.compose.ui.graphics.drawscope.DrawScope  String 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  	StrokeCap 0androidx.compose.ui.graphics.drawscope.DrawScope  
StrokeJoin 0androidx.compose.ui.graphics.drawscope.DrawScope  	TextAlign 0androidx.compose.ui.graphics.drawscope.DrawScope  	TextStyle 0androidx.compose.ui.graphics.drawscope.DrawScope  WaterAbyssBlue 0androidx.compose.ui.graphics.drawscope.DrawScope  
WaterDeepNavy 0androidx.compose.ui.graphics.drawscope.DrawScope  WaterDodgerBlue 0androidx.compose.ui.graphics.drawscope.DrawScope  WaterSkyBlue 0androidx.compose.ui.graphics.drawscope.DrawScope  WaterSteelBlue 0androidx.compose.ui.graphics.drawscope.DrawScope  abs 0androidx.compose.ui.graphics.drawscope.DrawScope  apply 0androidx.compose.ui.graphics.drawscope.DrawScope  coerceIn 0androidx.compose.ui.graphics.drawscope.DrawScope  cos 0androidx.compose.ui.graphics.drawscope.DrawScope  dashPathEffect 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  drawArc 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  	drawImage 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  drawPath 0androidx.compose.ui.graphics.drawscope.DrawScope  drawRect 0androidx.compose.ui.graphics.drawscope.DrawScope  drawSonarBottomContour 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawSonarFish 0androidx.compose.ui.graphics.drawscope.DrawScope  drawSonarUltrasonicData 0androidx.compose.ui.graphics.drawscope.DrawScope  drawText 0androidx.compose.ui.graphics.drawscope.DrawScope  first 0androidx.compose.ui.graphics.drawscope.DrawScope  floatArrayOf 0androidx.compose.ui.graphics.drawscope.DrawScope  forEachIndexed 0androidx.compose.ui.graphics.drawscope.DrawScope  format 0androidx.compose.ui.graphics.drawscope.DrawScope  	getOrNull 0androidx.compose.ui.graphics.drawscope.DrawScope  invoke 0androidx.compose.ui.graphics.drawscope.DrawScope  
isNotEmpty 0androidx.compose.ui.graphics.drawscope.DrawScope  kotlin 0androidx.compose.ui.graphics.drawscope.DrawScope  last 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  min 0androidx.compose.ui.graphics.drawscope.DrawScope  minOf 0androidx.compose.ui.graphics.drawscope.DrawScope  minOfOrNull 0androidx.compose.ui.graphics.drawscope.DrawScope  
mutableListOf 0androidx.compose.ui.graphics.drawscope.DrawScope  	nextFloat 0androidx.compose.ui.graphics.drawscope.DrawScope  radialGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  repeat 0androidx.compose.ui.graphics.drawscope.DrawScope  sin 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  sp 0androidx.compose.ui.graphics.drawscope.DrawScope  step 0androidx.compose.ui.graphics.drawscope.DrawScope  
sweepGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  takeLast 0androidx.compose.ui.graphics.drawscope.DrawScope  tint 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  until 0androidx.compose.ui.graphics.drawscope.DrawScope  verticalGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  Painter $androidx.compose.ui.graphics.painter  ImageVector #androidx.compose.ui.graphics.vector  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  detectVerticalDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  LocalConfiguration androidx.compose.ui.platform  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  LocalLayoutDirection androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  
imageResource androidx.compose.ui.res  painterResource androidx.compose.ui.res  TextLayoutResult androidx.compose.ui.text  TextMeasurer androidx.compose.ui.text  	TextStyle androidx.compose.ui.text  drawText androidx.compose.ui.text  rememberTextMeasurer androidx.compose.ui.text  size )androidx.compose.ui.text.TextLayoutResult  measure %androidx.compose.ui.text.TextMeasurer  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  	ExtraBold (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	ExtraBold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Left (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Left 2androidx.compose.ui.text.style.TextAlign.Companion  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  	IntOffset androidx.compose.ui.unit  IntSize androidx.compose.ui.unit  LayoutDirection androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  dp  androidx.compose.ui.unit.Density  toPx  androidx.compose.ui.unit.Density  plus androidx.compose.ui.unit.Dp  times androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  height  androidx.compose.ui.unit.IntSize  width  androidx.compose.ui.unit.IntSize  
Configuration #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  	Lifecycle #androidx.core.app.ComponentActivity  
LnnfilshTheme #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  Thread #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  WindowInsetsCompat #androidx.core.app.ComponentActivity  WindowInsetsControllerCompat #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  repeatOnLifecycle #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  FileProvider androidx.core.content  
getUriForFile "androidx.core.content.FileProvider  WindowCompat androidx.core.view  WindowInsetsCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  setDecorFitsSystemWindows androidx.core.view.WindowCompat  navigationBars *androidx.core.view.WindowInsetsCompat.Type  
statusBars *androidx.core.view.WindowInsetsCompat.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE /androidx.core.view.WindowInsetsControllerCompat  hide /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  show /androidx.core.view.WindowInsetsControllerCompat  systemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  	Lifecycle androidx.lifecycle  LifecycleCoroutineScope androidx.lifecycle  	ViewModel androidx.lifecycle  ViewModelProvider androidx.lifecycle  lifecycleScope androidx.lifecycle  repeatOnLifecycle androidx.lifecycle  viewModelScope androidx.lifecycle  State androidx.lifecycle.Lifecycle  CREATED "androidx.lifecycle.Lifecycle.State  launch *androidx.lifecycle.LifecycleCoroutineScope  Command androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  Dispatchers androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  FishData androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Log androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Random androidx.lifecycle.ViewModel  SensorDataType androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  System androidx.lifecycle.ViewModel  	TcpClient androidx.lifecycle.ViewModel  _batteryPercentage androidx.lifecycle.ViewModel  _connectionStatus androidx.lifecycle.ViewModel  
_currentDepth androidx.lifecycle.ViewModel  
_depthHistory androidx.lifecycle.ViewModel  
_errorMessage androidx.lifecycle.ViewModel  	_fishData androidx.lifecycle.ViewModel  
_isCollecting androidx.lifecycle.ViewModel  
_ledStatus androidx.lifecycle.ViewModel  _samplingInterval androidx.lifecycle.ViewModel  _sensitivity androidx.lifecycle.ViewModel  _ultrasonicData androidx.lifecycle.ViewModel  _waterTemperature androidx.lifecycle.ViewModel  abs androidx.lifecycle.ViewModel  apply androidx.lifecycle.ViewModel  cancel androidx.lifecycle.ViewModel  checkAlarmTrigger androidx.lifecycle.ViewModel  coerceIn androidx.lifecycle.ViewModel  
component1 androidx.lifecycle.ViewModel  
component2 androidx.lifecycle.ViewModel  delay androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  isFirstDataAfterConnection androidx.lifecycle.ViewModel  
isNotEmpty androidx.lifecycle.ViewModel  kotlin androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  listOf androidx.lifecycle.ViewModel  
mutableListOf androidx.lifecycle.ViewModel  	nextFloat androidx.lifecycle.ViewModel  nextInt androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  
plusAssign androidx.lifecycle.ViewModel  simulateFishData androidx.lifecycle.ViewModel  simulateUltrasonicData androidx.lifecycle.ViewModel  sin androidx.lifecycle.ViewModel  startCollecting androidx.lifecycle.ViewModel  
startsWith androidx.lifecycle.ViewModel  	tcpClient androidx.lifecycle.ViewModel  to androidx.lifecycle.ViewModel  
toMutableList androidx.lifecycle.ViewModel  until androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  withContext androidx.lifecycle.ViewModel  Factory $androidx.lifecycle.ViewModelProvider  	viewModel $androidx.lifecycle.viewmodel.compose  Bundle com.example.lnnfilsh  ComponentActivity com.example.lnnfilsh  
Configuration com.example.lnnfilsh  	Exception com.example.lnnfilsh  	Lifecycle com.example.lnnfilsh  
LnnfilshTheme com.example.lnnfilsh  Log com.example.lnnfilsh  MainActivity com.example.lnnfilsh  
MainScreen com.example.lnnfilsh  
MaterialTheme com.example.lnnfilsh  Modifier com.example.lnnfilsh  R com.example.lnnfilsh  Surface com.example.lnnfilsh  TAG com.example.lnnfilsh  Thread com.example.lnnfilsh  WindowCompat com.example.lnnfilsh  WindowInsetsCompat com.example.lnnfilsh  WindowInsetsControllerCompat com.example.lnnfilsh  
WindowManager com.example.lnnfilsh  fillMaxSize com.example.lnnfilsh  launch com.example.lnnfilsh  repeatOnLifecycle com.example.lnnfilsh  
setContent com.example.lnnfilsh  Bundle !com.example.lnnfilsh.MainActivity  
Configuration !com.example.lnnfilsh.MainActivity  	Exception !com.example.lnnfilsh.MainActivity  	Lifecycle !com.example.lnnfilsh.MainActivity  
LnnfilshTheme !com.example.lnnfilsh.MainActivity  Log !com.example.lnnfilsh.MainActivity  
MainScreen !com.example.lnnfilsh.MainActivity  
MaterialTheme !com.example.lnnfilsh.MainActivity  Modifier !com.example.lnnfilsh.MainActivity  Surface !com.example.lnnfilsh.MainActivity  TAG !com.example.lnnfilsh.MainActivity  Thread !com.example.lnnfilsh.MainActivity  WindowCompat !com.example.lnnfilsh.MainActivity  WindowInsetsCompat !com.example.lnnfilsh.MainActivity  WindowInsetsControllerCompat !com.example.lnnfilsh.MainActivity  
WindowManager !com.example.lnnfilsh.MainActivity  fillMaxSize !com.example.lnnfilsh.MainActivity  launch !com.example.lnnfilsh.MainActivity  lifecycleScope !com.example.lnnfilsh.MainActivity  repeatOnLifecycle !com.example.lnnfilsh.MainActivity  
setContent !com.example.lnnfilsh.MainActivity  setupExceptionHandler !com.example.lnnfilsh.MainActivity  window !com.example.lnnfilsh.MainActivity  windowInsetsController !com.example.lnnfilsh.MainActivity  
Configuration +com.example.lnnfilsh.MainActivity.Companion  	Lifecycle +com.example.lnnfilsh.MainActivity.Companion  
LnnfilshTheme +com.example.lnnfilsh.MainActivity.Companion  Log +com.example.lnnfilsh.MainActivity.Companion  
MainScreen +com.example.lnnfilsh.MainActivity.Companion  
MaterialTheme +com.example.lnnfilsh.MainActivity.Companion  Modifier +com.example.lnnfilsh.MainActivity.Companion  Surface +com.example.lnnfilsh.MainActivity.Companion  TAG +com.example.lnnfilsh.MainActivity.Companion  Thread +com.example.lnnfilsh.MainActivity.Companion  WindowCompat +com.example.lnnfilsh.MainActivity.Companion  WindowInsetsCompat +com.example.lnnfilsh.MainActivity.Companion  WindowInsetsControllerCompat +com.example.lnnfilsh.MainActivity.Companion  
WindowManager +com.example.lnnfilsh.MainActivity.Companion  fillMaxSize +com.example.lnnfilsh.MainActivity.Companion  launch +com.example.lnnfilsh.MainActivity.Companion  lifecycleScope +com.example.lnnfilsh.MainActivity.Companion  repeatOnLifecycle +com.example.lnnfilsh.MainActivity.Companion  
setContent +com.example.lnnfilsh.MainActivity.Companion  da com.example.lnnfilsh.R.drawable  
ic_arrow_down com.example.lnnfilsh.R.drawable  
ic_arrow_left com.example.lnnfilsh.R.drawable  ic_arrow_right com.example.lnnfilsh.R.drawable  ic_arrow_up com.example.lnnfilsh.R.drawable  
ic_battery_25 com.example.lnnfilsh.R.drawable  
ic_battery_50 com.example.lnnfilsh.R.drawable  
ic_battery_75 com.example.lnnfilsh.R.drawable  ic_battery_full com.example.lnnfilsh.R.drawable  ic_battery_low com.example.lnnfilsh.R.drawable  ic_bluetooth com.example.lnnfilsh.R.drawable  ic_depth com.example.lnnfilsh.R.drawable  ic_direction_controls com.example.lnnfilsh.R.drawable  ic_fish com.example.lnnfilsh.R.drawable  ic_radar com.example.lnnfilsh.R.drawable  
ic_screenshot com.example.lnnfilsh.R.drawable  ic_temperature com.example.lnnfilsh.R.drawable  ic_vibration com.example.lnnfilsh.R.drawable  ic_wifi com.example.lnnfilsh.R.drawable  xiao com.example.lnnfilsh.R.drawable  zhong com.example.lnnfilsh.R.drawable  beep com.example.lnnfilsh.R.raw  Any com.example.lnnfilsh.model  Boolean com.example.lnnfilsh.model  	ByteArray com.example.lnnfilsh.model  Command com.example.lnnfilsh.model  CommandResponse com.example.lnnfilsh.model  	DataFrame com.example.lnnfilsh.model  DeviceStatus com.example.lnnfilsh.model  	ErrorCode com.example.lnnfilsh.model  FishData com.example.lnnfilsh.model  FishSize com.example.lnnfilsh.model  Float com.example.lnnfilsh.model  Int com.example.lnnfilsh.model  List com.example.lnnfilsh.model  Long com.example.lnnfilsh.model  Map com.example.lnnfilsh.model  
SensorData com.example.lnnfilsh.model  SensorDataType com.example.lnnfilsh.model  SerializedName com.example.lnnfilsh.model  String com.example.lnnfilsh.model  System com.example.lnnfilsh.model  UltrasonicData com.example.lnnfilsh.model  
contentEquals com.example.lnnfilsh.model  contentHashCode com.example.lnnfilsh.model  emptyMap com.example.lnnfilsh.model  	javaClass com.example.lnnfilsh.model  Any "com.example.lnnfilsh.model.Command  Command "com.example.lnnfilsh.model.Command  	Companion "com.example.lnnfilsh.model.Command  
MOVE_BACKWARD "com.example.lnnfilsh.model.Command  MOVE_FORWARD "com.example.lnnfilsh.model.Command  Map "com.example.lnnfilsh.model.Command  STOP "com.example.lnnfilsh.model.Command  String "com.example.lnnfilsh.model.Command  	TURN_LEFT "com.example.lnnfilsh.model.Command  
TURN_RIGHT "com.example.lnnfilsh.model.Command  params "com.example.lnnfilsh.model.Command  type "com.example.lnnfilsh.model.Command  Command ,com.example.lnnfilsh.model.Command.Companion  
MOVE_BACKWARD ,com.example.lnnfilsh.model.Command.Companion  MOVE_FORWARD ,com.example.lnnfilsh.model.Command.Companion  STOP ,com.example.lnnfilsh.model.Command.Companion  	TURN_LEFT ,com.example.lnnfilsh.model.Command.Companion  
TURN_RIGHT ,com.example.lnnfilsh.model.Command.Companion  checksum $com.example.lnnfilsh.model.DataFrame  
contentEquals $com.example.lnnfilsh.model.DataFrame  contentHashCode $com.example.lnnfilsh.model.DataFrame  data $com.example.lnnfilsh.model.DataFrame  
dataLength $com.example.lnnfilsh.model.DataFrame  header $com.example.lnnfilsh.model.DataFrame  	javaClass $com.example.lnnfilsh.model.DataFrame  type $com.example.lnnfilsh.model.DataFrame  batteryPercentage 'com.example.lnnfilsh.model.DeviceStatus  isCollecting 'com.example.lnnfilsh.model.DeviceStatus  	ledStatus 'com.example.lnnfilsh.model.DeviceStatus  depth #com.example.lnnfilsh.model.FishData  size #com.example.lnnfilsh.model.FishData  x #com.example.lnnfilsh.model.FishData  	timestamp %com.example.lnnfilsh.model.SensorData  type %com.example.lnnfilsh.model.SensorData  value %com.example.lnnfilsh.model.SensorData  values %com.example.lnnfilsh.model.SensorData  DEPTH )com.example.lnnfilsh.model.SensorDataType  FISH )com.example.lnnfilsh.model.SensorDataType  SensorDataType )com.example.lnnfilsh.model.SensorDataType  Temperature )com.example.lnnfilsh.model.SensorDataType  
ULTRASONIC )com.example.lnnfilsh.model.SensorDataType  UltrasonicDistance )com.example.lnnfilsh.model.SensorDataType  Boolean com.example.lnnfilsh.network  Byte com.example.lnnfilsh.network  	ByteArray com.example.lnnfilsh.network  
ByteBuffer com.example.lnnfilsh.network  	ByteOrder com.example.lnnfilsh.network  CMD_HEADER_1 com.example.lnnfilsh.network  CMD_HEADER_2 com.example.lnnfilsh.network  CMD_MOVE_BACKWARD com.example.lnnfilsh.network  CMD_MOVE_FORWARD com.example.lnnfilsh.network  CMD_RESET_DEVICE com.example.lnnfilsh.network  CMD_SET_PARAMS com.example.lnnfilsh.network  CMD_START_COLLECTING com.example.lnnfilsh.network  CMD_STOP com.example.lnnfilsh.network  CMD_STOP_COLLECTING com.example.lnnfilsh.network  CMD_TOGGLE_LED com.example.lnnfilsh.network  
CMD_TURN_LEFT com.example.lnnfilsh.network  CMD_TURN_RIGHT com.example.lnnfilsh.network  COMMAND_FRAME_SIZE com.example.lnnfilsh.network  Command com.example.lnnfilsh.network  CommandResponse com.example.lnnfilsh.network  CoroutineScope com.example.lnnfilsh.network  DeviceStatus com.example.lnnfilsh.network  Dispatchers com.example.lnnfilsh.network  	Exception com.example.lnnfilsh.network  FRAME_HEADER_1 com.example.lnnfilsh.network  FRAME_HEADER_2 com.example.lnnfilsh.network  Float com.example.lnnfilsh.network  
FloatArray com.example.lnnfilsh.network  Gson com.example.lnnfilsh.network  InetSocketAddress com.example.lnnfilsh.network  InputStream com.example.lnnfilsh.network  Int com.example.lnnfilsh.network  Job com.example.lnnfilsh.network  Log com.example.lnnfilsh.network  MutableSharedFlow com.example.lnnfilsh.network  Number com.example.lnnfilsh.network  OutputStream com.example.lnnfilsh.network  RESPONSE_FRAME_SIZE com.example.lnnfilsh.network  RESP_COMMAND_ACK com.example.lnnfilsh.network  RESP_DEPTH_DATA com.example.lnnfilsh.network  RESP_FISH_DATA com.example.lnnfilsh.network  RESP_STATUS_DATA com.example.lnnfilsh.network  RESP_ULTRASONIC_DATA com.example.lnnfilsh.network  
SensorData com.example.lnnfilsh.network  SensorDataType com.example.lnnfilsh.network  
SharedFlow com.example.lnnfilsh.network  Socket com.example.lnnfilsh.network  String com.example.lnnfilsh.network  System com.example.lnnfilsh.network  TAG com.example.lnnfilsh.network  	TcpClient com.example.lnnfilsh.network  Thread com.example.lnnfilsh.network  _commandResponseFlow com.example.lnnfilsh.network  _connectionStatusFlow com.example.lnnfilsh.network  _deviceStatusFlow com.example.lnnfilsh.network  _sensorDataFlow com.example.lnnfilsh.network  and com.example.lnnfilsh.network  byteArrayOf com.example.lnnfilsh.network  closeConnection com.example.lnnfilsh.network  coerceIn com.example.lnnfilsh.network  
component1 com.example.lnnfilsh.network  
component2 com.example.lnnfilsh.network  connect com.example.lnnfilsh.network  connectionTimeout com.example.lnnfilsh.network  
contentEquals com.example.lnnfilsh.network  copyOf com.example.lnnfilsh.network  copyOfRange com.example.lnnfilsh.network  delay com.example.lnnfilsh.network  endsWith com.example.lnnfilsh.network  gson com.example.lnnfilsh.network  handleConnectionError com.example.lnnfilsh.network  indices com.example.lnnfilsh.network  inputStream com.example.lnnfilsh.network  invoke com.example.lnnfilsh.network  isConnected com.example.lnnfilsh.network  
isNotEmpty com.example.lnnfilsh.network  java com.example.lnnfilsh.network  joinToString com.example.lnnfilsh.network  last com.example.lnnfilsh.network  launch com.example.lnnfilsh.network  mapOf com.example.lnnfilsh.network  	mapValues com.example.lnnfilsh.network  minOf com.example.lnnfilsh.network  mutableMapOf com.example.lnnfilsh.network  outputStream com.example.lnnfilsh.network  plus com.example.lnnfilsh.network  processDataFrames com.example.lnnfilsh.network  reconnectAttempts com.example.lnnfilsh.network  run com.example.lnnfilsh.network  sendBinaryCommand com.example.lnnfilsh.network  set com.example.lnnfilsh.network  
setParameters com.example.lnnfilsh.network  socket com.example.lnnfilsh.network  startHeartbeat com.example.lnnfilsh.network  startReceiving com.example.lnnfilsh.network  
startsWith com.example.lnnfilsh.network  take com.example.lnnfilsh.network  to com.example.lnnfilsh.network  toByteArray com.example.lnnfilsh.network  	toggleLed com.example.lnnfilsh.network  until com.example.lnnfilsh.network  withContext com.example.lnnfilsh.network  withTimeoutOrNull com.example.lnnfilsh.network  Boolean &com.example.lnnfilsh.network.TcpClient  Byte &com.example.lnnfilsh.network.TcpClient  	ByteArray &com.example.lnnfilsh.network.TcpClient  
ByteBuffer &com.example.lnnfilsh.network.TcpClient  	ByteOrder &com.example.lnnfilsh.network.TcpClient  CMD_HEADER_1 &com.example.lnnfilsh.network.TcpClient  CMD_HEADER_2 &com.example.lnnfilsh.network.TcpClient  CMD_MOVE_BACKWARD &com.example.lnnfilsh.network.TcpClient  CMD_MOVE_FORWARD &com.example.lnnfilsh.network.TcpClient  CMD_RESET_DEVICE &com.example.lnnfilsh.network.TcpClient  CMD_SET_PARAMS &com.example.lnnfilsh.network.TcpClient  CMD_START_COLLECTING &com.example.lnnfilsh.network.TcpClient  CMD_STOP &com.example.lnnfilsh.network.TcpClient  CMD_STOP_COLLECTING &com.example.lnnfilsh.network.TcpClient  CMD_TOGGLE_LED &com.example.lnnfilsh.network.TcpClient  
CMD_TURN_LEFT &com.example.lnnfilsh.network.TcpClient  CMD_TURN_RIGHT &com.example.lnnfilsh.network.TcpClient  COMMAND_FRAME_SIZE &com.example.lnnfilsh.network.TcpClient  Command &com.example.lnnfilsh.network.TcpClient  CommandResponse &com.example.lnnfilsh.network.TcpClient  CoroutineScope &com.example.lnnfilsh.network.TcpClient  DeviceStatus &com.example.lnnfilsh.network.TcpClient  Dispatchers &com.example.lnnfilsh.network.TcpClient  	Exception &com.example.lnnfilsh.network.TcpClient  FRAME_HEADER_1 &com.example.lnnfilsh.network.TcpClient  FRAME_HEADER_2 &com.example.lnnfilsh.network.TcpClient  Float &com.example.lnnfilsh.network.TcpClient  
FloatArray &com.example.lnnfilsh.network.TcpClient  Gson &com.example.lnnfilsh.network.TcpClient  InetSocketAddress &com.example.lnnfilsh.network.TcpClient  InputStream &com.example.lnnfilsh.network.TcpClient  Int &com.example.lnnfilsh.network.TcpClient  Job &com.example.lnnfilsh.network.TcpClient  Log &com.example.lnnfilsh.network.TcpClient  MutableSharedFlow &com.example.lnnfilsh.network.TcpClient  Number &com.example.lnnfilsh.network.TcpClient  OutputStream &com.example.lnnfilsh.network.TcpClient  RESPONSE_FRAME_SIZE &com.example.lnnfilsh.network.TcpClient  RESP_COMMAND_ACK &com.example.lnnfilsh.network.TcpClient  RESP_DEPTH_DATA &com.example.lnnfilsh.network.TcpClient  RESP_FISH_DATA &com.example.lnnfilsh.network.TcpClient  RESP_STATUS_DATA &com.example.lnnfilsh.network.TcpClient  RESP_ULTRASONIC_DATA &com.example.lnnfilsh.network.TcpClient  
SensorData &com.example.lnnfilsh.network.TcpClient  SensorDataType &com.example.lnnfilsh.network.TcpClient  
SharedFlow &com.example.lnnfilsh.network.TcpClient  Socket &com.example.lnnfilsh.network.TcpClient  String &com.example.lnnfilsh.network.TcpClient  System &com.example.lnnfilsh.network.TcpClient  TAG &com.example.lnnfilsh.network.TcpClient  Thread &com.example.lnnfilsh.network.TcpClient  _commandResponseFlow &com.example.lnnfilsh.network.TcpClient  _connectionStatusFlow &com.example.lnnfilsh.network.TcpClient  _deviceStatusFlow &com.example.lnnfilsh.network.TcpClient  _sensorDataFlow &com.example.lnnfilsh.network.TcpClient  and &com.example.lnnfilsh.network.TcpClient  byteArrayOf &com.example.lnnfilsh.network.TcpClient  calculateCRC16 &com.example.lnnfilsh.network.TcpClient  closeConnection &com.example.lnnfilsh.network.TcpClient  coerceIn &com.example.lnnfilsh.network.TcpClient  
component1 &com.example.lnnfilsh.network.TcpClient  
component2 &com.example.lnnfilsh.network.TcpClient  connect &com.example.lnnfilsh.network.TcpClient  
connectionJob &com.example.lnnfilsh.network.TcpClient  connectionStatusFlow &com.example.lnnfilsh.network.TcpClient  connectionTimeout &com.example.lnnfilsh.network.TcpClient  
contentEquals &com.example.lnnfilsh.network.TcpClient  copyOf &com.example.lnnfilsh.network.TcpClient  copyOfRange &com.example.lnnfilsh.network.TcpClient  coroutineScope &com.example.lnnfilsh.network.TcpClient  delay &com.example.lnnfilsh.network.TcpClient  deviceStatusFlow &com.example.lnnfilsh.network.TcpClient  
disconnect &com.example.lnnfilsh.network.TcpClient  endsWith &com.example.lnnfilsh.network.TcpClient  gson &com.example.lnnfilsh.network.TcpClient  handleConnectionError &com.example.lnnfilsh.network.TcpClient  heartbeatJob &com.example.lnnfilsh.network.TcpClient  indices &com.example.lnnfilsh.network.TcpClient  inputStream &com.example.lnnfilsh.network.TcpClient  invoke &com.example.lnnfilsh.network.TcpClient  isActive &com.example.lnnfilsh.network.TcpClient  isConnected &com.example.lnnfilsh.network.TcpClient  
isNotEmpty &com.example.lnnfilsh.network.TcpClient  java &com.example.lnnfilsh.network.TcpClient  joinToString &com.example.lnnfilsh.network.TcpClient  last &com.example.lnnfilsh.network.TcpClient  launch &com.example.lnnfilsh.network.TcpClient  mapOf &com.example.lnnfilsh.network.TcpClient  	mapValues &com.example.lnnfilsh.network.TcpClient  maxReconnectAttempts &com.example.lnnfilsh.network.TcpClient  minOf &com.example.lnnfilsh.network.TcpClient  mutableMapOf &com.example.lnnfilsh.network.TcpClient  outputStream &com.example.lnnfilsh.network.TcpClient  plus &com.example.lnnfilsh.network.TcpClient  processDataFrames &com.example.lnnfilsh.network.TcpClient  processJsonData &com.example.lnnfilsh.network.TcpClient  processReceivedData &com.example.lnnfilsh.network.TcpClient  
receiveJob &com.example.lnnfilsh.network.TcpClient  reconnectAttempts &com.example.lnnfilsh.network.TcpClient  resetDevice &com.example.lnnfilsh.network.TcpClient  run &com.example.lnnfilsh.network.TcpClient  sendBinaryCommand &com.example.lnnfilsh.network.TcpClient  sendCommand &com.example.lnnfilsh.network.TcpClient  sensorDataFlow &com.example.lnnfilsh.network.TcpClient  
serverAddress &com.example.lnnfilsh.network.TcpClient  
serverPort &com.example.lnnfilsh.network.TcpClient  set &com.example.lnnfilsh.network.TcpClient  
setParameters &com.example.lnnfilsh.network.TcpClient  socket &com.example.lnnfilsh.network.TcpClient  startCollecting &com.example.lnnfilsh.network.TcpClient  startHeartbeat &com.example.lnnfilsh.network.TcpClient  startReceiving &com.example.lnnfilsh.network.TcpClient  
startsWith &com.example.lnnfilsh.network.TcpClient  stopCollecting &com.example.lnnfilsh.network.TcpClient  take &com.example.lnnfilsh.network.TcpClient  to &com.example.lnnfilsh.network.TcpClient  toByteArray &com.example.lnnfilsh.network.TcpClient  	toggleLed &com.example.lnnfilsh.network.TcpClient  until &com.example.lnnfilsh.network.TcpClient  withContext &com.example.lnnfilsh.network.TcpClient  withTimeoutOrNull &com.example.lnnfilsh.network.TcpClient  	ByteArray 0com.example.lnnfilsh.network.TcpClient.Companion  
ByteBuffer 0com.example.lnnfilsh.network.TcpClient.Companion  	ByteOrder 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_HEADER_1 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_HEADER_2 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_MOVE_BACKWARD 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_MOVE_FORWARD 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_RESET_DEVICE 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_SET_PARAMS 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_START_COLLECTING 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_STOP 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_STOP_COLLECTING 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_TOGGLE_LED 0com.example.lnnfilsh.network.TcpClient.Companion  
CMD_TURN_LEFT 0com.example.lnnfilsh.network.TcpClient.Companion  CMD_TURN_RIGHT 0com.example.lnnfilsh.network.TcpClient.Companion  COMMAND_FRAME_SIZE 0com.example.lnnfilsh.network.TcpClient.Companion  CommandResponse 0com.example.lnnfilsh.network.TcpClient.Companion  CoroutineScope 0com.example.lnnfilsh.network.TcpClient.Companion  DeviceStatus 0com.example.lnnfilsh.network.TcpClient.Companion  Dispatchers 0com.example.lnnfilsh.network.TcpClient.Companion  FRAME_HEADER_1 0com.example.lnnfilsh.network.TcpClient.Companion  FRAME_HEADER_2 0com.example.lnnfilsh.network.TcpClient.Companion  
FloatArray 0com.example.lnnfilsh.network.TcpClient.Companion  Gson 0com.example.lnnfilsh.network.TcpClient.Companion  InetSocketAddress 0com.example.lnnfilsh.network.TcpClient.Companion  Log 0com.example.lnnfilsh.network.TcpClient.Companion  MutableSharedFlow 0com.example.lnnfilsh.network.TcpClient.Companion  RESPONSE_FRAME_SIZE 0com.example.lnnfilsh.network.TcpClient.Companion  RESP_COMMAND_ACK 0com.example.lnnfilsh.network.TcpClient.Companion  RESP_DEPTH_DATA 0com.example.lnnfilsh.network.TcpClient.Companion  RESP_FISH_DATA 0com.example.lnnfilsh.network.TcpClient.Companion  RESP_STATUS_DATA 0com.example.lnnfilsh.network.TcpClient.Companion  RESP_ULTRASONIC_DATA 0com.example.lnnfilsh.network.TcpClient.Companion  
SensorData 0com.example.lnnfilsh.network.TcpClient.Companion  SensorDataType 0com.example.lnnfilsh.network.TcpClient.Companion  Socket 0com.example.lnnfilsh.network.TcpClient.Companion  String 0com.example.lnnfilsh.network.TcpClient.Companion  System 0com.example.lnnfilsh.network.TcpClient.Companion  TAG 0com.example.lnnfilsh.network.TcpClient.Companion  Thread 0com.example.lnnfilsh.network.TcpClient.Companion  _commandResponseFlow 0com.example.lnnfilsh.network.TcpClient.Companion  _connectionStatusFlow 0com.example.lnnfilsh.network.TcpClient.Companion  _deviceStatusFlow 0com.example.lnnfilsh.network.TcpClient.Companion  _sensorDataFlow 0com.example.lnnfilsh.network.TcpClient.Companion  and 0com.example.lnnfilsh.network.TcpClient.Companion  byteArrayOf 0com.example.lnnfilsh.network.TcpClient.Companion  closeConnection 0com.example.lnnfilsh.network.TcpClient.Companion  coerceIn 0com.example.lnnfilsh.network.TcpClient.Companion  
component1 0com.example.lnnfilsh.network.TcpClient.Companion  
component2 0com.example.lnnfilsh.network.TcpClient.Companion  connect 0com.example.lnnfilsh.network.TcpClient.Companion  connectionTimeout 0com.example.lnnfilsh.network.TcpClient.Companion  
contentEquals 0com.example.lnnfilsh.network.TcpClient.Companion  copyOf 0com.example.lnnfilsh.network.TcpClient.Companion  copyOfRange 0com.example.lnnfilsh.network.TcpClient.Companion  delay 0com.example.lnnfilsh.network.TcpClient.Companion  endsWith 0com.example.lnnfilsh.network.TcpClient.Companion  gson 0com.example.lnnfilsh.network.TcpClient.Companion  handleConnectionError 0com.example.lnnfilsh.network.TcpClient.Companion  indices 0com.example.lnnfilsh.network.TcpClient.Companion  inputStream 0com.example.lnnfilsh.network.TcpClient.Companion  invoke 0com.example.lnnfilsh.network.TcpClient.Companion  isActive 0com.example.lnnfilsh.network.TcpClient.Companion  isConnected 0com.example.lnnfilsh.network.TcpClient.Companion  
isNotEmpty 0com.example.lnnfilsh.network.TcpClient.Companion  java 0com.example.lnnfilsh.network.TcpClient.Companion  joinToString 0com.example.lnnfilsh.network.TcpClient.Companion  last 0com.example.lnnfilsh.network.TcpClient.Companion  launch 0com.example.lnnfilsh.network.TcpClient.Companion  mapOf 0com.example.lnnfilsh.network.TcpClient.Companion  	mapValues 0com.example.lnnfilsh.network.TcpClient.Companion  minOf 0com.example.lnnfilsh.network.TcpClient.Companion  mutableMapOf 0com.example.lnnfilsh.network.TcpClient.Companion  outputStream 0com.example.lnnfilsh.network.TcpClient.Companion  plus 0com.example.lnnfilsh.network.TcpClient.Companion  processDataFrames 0com.example.lnnfilsh.network.TcpClient.Companion  reconnectAttempts 0com.example.lnnfilsh.network.TcpClient.Companion  run 0com.example.lnnfilsh.network.TcpClient.Companion  sendBinaryCommand 0com.example.lnnfilsh.network.TcpClient.Companion  set 0com.example.lnnfilsh.network.TcpClient.Companion  
setParameters 0com.example.lnnfilsh.network.TcpClient.Companion  socket 0com.example.lnnfilsh.network.TcpClient.Companion  startHeartbeat 0com.example.lnnfilsh.network.TcpClient.Companion  startReceiving 0com.example.lnnfilsh.network.TcpClient.Companion  
startsWith 0com.example.lnnfilsh.network.TcpClient.Companion  take 0com.example.lnnfilsh.network.TcpClient.Companion  to 0com.example.lnnfilsh.network.TcpClient.Companion  toByteArray 0com.example.lnnfilsh.network.TcpClient.Companion  	toggleLed 0com.example.lnnfilsh.network.TcpClient.Companion  until 0com.example.lnnfilsh.network.TcpClient.Companion  withContext 0com.example.lnnfilsh.network.TcpClient.Companion  withTimeoutOrNull 0com.example.lnnfilsh.network.TcpClient.Companion  
AccentBlue "com.example.lnnfilsh.ui.components  AccentGreen "com.example.lnnfilsh.ui.components  	Alignment "com.example.lnnfilsh.ui.components  Arrangement "com.example.lnnfilsh.ui.components  	BlendMode "com.example.lnnfilsh.ui.components  Boolean "com.example.lnnfilsh.ui.components  BottomControlBar "com.example.lnnfilsh.ui.components  Box "com.example.lnnfilsh.ui.components  BrightAccent "com.example.lnnfilsh.ui.components  Brush "com.example.lnnfilsh.ui.components  Button "com.example.lnnfilsh.ui.components  ButtonDefaults "com.example.lnnfilsh.ui.components  Canvas "com.example.lnnfilsh.ui.components  Card "com.example.lnnfilsh.ui.components  CardDefaults "com.example.lnnfilsh.ui.components  CircleIconButton "com.example.lnnfilsh.ui.components  CircleShape "com.example.lnnfilsh.ui.components  Color "com.example.lnnfilsh.ui.components  ColorFilter "com.example.lnnfilsh.ui.components  Column "com.example.lnnfilsh.ui.components  
Composable "com.example.lnnfilsh.ui.components  ConnectedGreen "com.example.lnnfilsh.ui.components  ControlButtonBg "com.example.lnnfilsh.ui.components  ControlButtonTextColor "com.example.lnnfilsh.ui.components  ControlPanel "com.example.lnnfilsh.ui.components  
DepthChart "com.example.lnnfilsh.ui.components  DepthChartWithLabels "com.example.lnnfilsh.ui.components  DisconnectedRed "com.example.lnnfilsh.ui.components  Dp "com.example.lnnfilsh.ui.components  	DrawScope "com.example.lnnfilsh.ui.components  	Exception "com.example.lnnfilsh.ui.components  FastOutSlowInEasing "com.example.lnnfilsh.ui.components  FishData "com.example.lnnfilsh.ui.components  
FishIconWhite "com.example.lnnfilsh.ui.components  FishIconYellow "com.example.lnnfilsh.ui.components  Float "com.example.lnnfilsh.ui.components  FloatingStatusPanel "com.example.lnnfilsh.ui.components  
FontWeight "com.example.lnnfilsh.ui.components  Icon "com.example.lnnfilsh.ui.components  
IconButton "com.example.lnnfilsh.ui.components  Icons "com.example.lnnfilsh.ui.components  ImageBitmap "com.example.lnnfilsh.ui.components  ImageVector "com.example.lnnfilsh.ui.components  Int "com.example.lnnfilsh.ui.components  	IntOffset "com.example.lnnfilsh.ui.components  IntSize "com.example.lnnfilsh.ui.components  LinearEasing "com.example.lnnfilsh.ui.components  List "com.example.lnnfilsh.ui.components  Log "com.example.lnnfilsh.ui.components  
MaterialTheme "com.example.lnnfilsh.ui.components  Modifier "com.example.lnnfilsh.ui.components  Offset "com.example.lnnfilsh.ui.components  PI "com.example.lnnfilsh.ui.components  Painter "com.example.lnnfilsh.ui.components  Pair "com.example.lnnfilsh.ui.components  Path "com.example.lnnfilsh.ui.components  
PathEffect "com.example.lnnfilsh.ui.components  R "com.example.lnnfilsh.ui.components  Rect "com.example.lnnfilsh.ui.components  
RepeatMode "com.example.lnnfilsh.ui.components  RiverbedDarkOrange "com.example.lnnfilsh.ui.components  RiverbedDeepOrange "com.example.lnnfilsh.ui.components  RiverbedMidOrange "com.example.lnnfilsh.ui.components  RiverbedSoilBrown "com.example.lnnfilsh.ui.components  RiverbedSurfaceGold "com.example.lnnfilsh.ui.components  RoundedCornerShape "com.example.lnnfilsh.ui.components  Row "com.example.lnnfilsh.ui.components  Size "com.example.lnnfilsh.ui.components  Slider "com.example.lnnfilsh.ui.components  SliderDefaults "com.example.lnnfilsh.ui.components  SonarDepthChart "com.example.lnnfilsh.ui.components  Spacer "com.example.lnnfilsh.ui.components  String "com.example.lnnfilsh.ui.components  Stroke "com.example.lnnfilsh.ui.components  	StrokeCap "com.example.lnnfilsh.ui.components  
StrokeJoin "com.example.lnnfilsh.ui.components  Switch "com.example.lnnfilsh.ui.components  SwitchDefaults "com.example.lnnfilsh.ui.components  Text "com.example.lnnfilsh.ui.components  	TextAlign "com.example.lnnfilsh.ui.components  	TextStyle "com.example.lnnfilsh.ui.components  Unit "com.example.lnnfilsh.ui.components  WaterAbyssBlue "com.example.lnnfilsh.ui.components  
WaterDeepNavy "com.example.lnnfilsh.ui.components  WaterDodgerBlue "com.example.lnnfilsh.ui.components  WaterSkyBlue "com.example.lnnfilsh.ui.components  WaterSteelBlue "com.example.lnnfilsh.ui.components  abs "com.example.lnnfilsh.ui.components  alpha "com.example.lnnfilsh.ui.components  androidx "com.example.lnnfilsh.ui.components  animateFloat "com.example.lnnfilsh.ui.components  any "com.example.lnnfilsh.ui.components  apply "com.example.lnnfilsh.ui.components  
background "com.example.lnnfilsh.ui.components  buttonColors "com.example.lnnfilsh.ui.components  
cardColors "com.example.lnnfilsh.ui.components  
cardElevation "com.example.lnnfilsh.ui.components  	clickable "com.example.lnnfilsh.ui.components  clip "com.example.lnnfilsh.ui.components  clipToBounds "com.example.lnnfilsh.ui.components  coerceIn "com.example.lnnfilsh.ui.components  colors "com.example.lnnfilsh.ui.components  cos "com.example.lnnfilsh.ui.components  dashPathEffect "com.example.lnnfilsh.ui.components  drawSonarBottomContour "com.example.lnnfilsh.ui.components  
drawSonarFish "com.example.lnnfilsh.ui.components  drawSonarUltrasonicData "com.example.lnnfilsh.ui.components  	emptyList "com.example.lnnfilsh.ui.components  fillMaxSize "com.example.lnnfilsh.ui.components  fillMaxWidth "com.example.lnnfilsh.ui.components  first "com.example.lnnfilsh.ui.components  floatArrayOf "com.example.lnnfilsh.ui.components  forEach "com.example.lnnfilsh.ui.components  forEachIndexed "com.example.lnnfilsh.ui.components  format "com.example.lnnfilsh.ui.components  	getOrNull "com.example.lnnfilsh.ui.components  getValue "com.example.lnnfilsh.ui.components  height "com.example.lnnfilsh.ui.components  
imageResource "com.example.lnnfilsh.ui.components  infiniteRepeatable "com.example.lnnfilsh.ui.components  invoke "com.example.lnnfilsh.ui.components  
isNotEmpty "com.example.lnnfilsh.ui.components  kotlin "com.example.lnnfilsh.ui.components  last "com.example.lnnfilsh.ui.components  listOf "com.example.lnnfilsh.ui.components  	maxOrNull "com.example.lnnfilsh.ui.components  min "com.example.lnnfilsh.ui.components  minOf "com.example.lnnfilsh.ui.components  minOfOrNull "com.example.lnnfilsh.ui.components  
mutableListOf "com.example.lnnfilsh.ui.components  	nextFloat "com.example.lnnfilsh.ui.components  padding "com.example.lnnfilsh.ui.components  painterResource "com.example.lnnfilsh.ui.components  provideDelegate "com.example.lnnfilsh.ui.components  radialGradient "com.example.lnnfilsh.ui.components  rangeTo "com.example.lnnfilsh.ui.components  remember "com.example.lnnfilsh.ui.components  rememberInfiniteTransition "com.example.lnnfilsh.ui.components  repeat "com.example.lnnfilsh.ui.components  sin "com.example.lnnfilsh.ui.components  size "com.example.lnnfilsh.ui.components  spacedBy "com.example.lnnfilsh.ui.components  step "com.example.lnnfilsh.ui.components  
sweepGradient "com.example.lnnfilsh.ui.components  takeLast "com.example.lnnfilsh.ui.components  tint "com.example.lnnfilsh.ui.components  tween "com.example.lnnfilsh.ui.components  until "com.example.lnnfilsh.ui.components  verticalGradient "com.example.lnnfilsh.ui.components  weight "com.example.lnnfilsh.ui.components  width "com.example.lnnfilsh.ui.components  with "com.example.lnnfilsh.ui.components  compose +com.example.lnnfilsh.ui.components.androidx  ui 3com.example.lnnfilsh.ui.components.androidx.compose  text 6com.example.lnnfilsh.ui.components.androidx.compose.ui  TextMeasurer ;com.example.lnnfilsh.ui.components.androidx.compose.ui.text  
AccentBlue com.example.lnnfilsh.ui.screens  	Alignment com.example.lnnfilsh.ui.screens  Application com.example.lnnfilsh.ui.screens  Arrangement com.example.lnnfilsh.ui.screens  Box com.example.lnnfilsh.ui.screens  Card com.example.lnnfilsh.ui.screens  CardDefaults com.example.lnnfilsh.ui.screens  Color com.example.lnnfilsh.ui.screens  Column com.example.lnnfilsh.ui.screens  
Composable com.example.lnnfilsh.ui.screens  Context com.example.lnnfilsh.ui.screens  DepthChartWithLabels com.example.lnnfilsh.ui.screens  	Exception com.example.lnnfilsh.ui.screens  FishDetectorViewModel com.example.lnnfilsh.ui.screens  FloatingActionButton com.example.lnnfilsh.ui.screens  Handler com.example.lnnfilsh.ui.screens  HorizontalDivider com.example.lnnfilsh.ui.screens  Icon com.example.lnnfilsh.ui.screens  
IconButton com.example.lnnfilsh.ui.screens  Icons com.example.lnnfilsh.ui.screens  Log com.example.lnnfilsh.ui.screens  Looper com.example.lnnfilsh.ui.screens  
MainScreen com.example.lnnfilsh.ui.screens  MediaPlayer com.example.lnnfilsh.ui.screens  Modifier com.example.lnnfilsh.ui.screens  R com.example.lnnfilsh.ui.screens  RoundedCornerShape com.example.lnnfilsh.ui.screens  Row com.example.lnnfilsh.ui.screens  ScreenshotUtil com.example.lnnfilsh.ui.screens  SecondaryIndicator com.example.lnnfilsh.ui.screens  Slider com.example.lnnfilsh.ui.screens  SliderDefaults com.example.lnnfilsh.ui.screens  Spacer com.example.lnnfilsh.ui.screens  Switch com.example.lnnfilsh.ui.screens  SwitchDefaults com.example.lnnfilsh.ui.screens  Tab com.example.lnnfilsh.ui.screens  TabRow com.example.lnnfilsh.ui.screens  TabRowDefaults com.example.lnnfilsh.ui.screens  Text com.example.lnnfilsh.ui.screens  Thread com.example.lnnfilsh.ui.screens  Toast com.example.lnnfilsh.ui.screens  Unit com.example.lnnfilsh.ui.screens  
WaterDeepBlue com.example.lnnfilsh.ui.screens  WindowInsets com.example.lnnfilsh.ui.screens  align com.example.lnnfilsh.ui.screens  alpha com.example.lnnfilsh.ui.screens  android com.example.lnnfilsh.ui.screens  
background com.example.lnnfilsh.ui.screens  
cardColors com.example.lnnfilsh.ui.screens  
cardElevation com.example.lnnfilsh.ui.screens  	clickable com.example.lnnfilsh.ui.screens  clip com.example.lnnfilsh.ui.screens  colors com.example.lnnfilsh.ui.screens  fillMaxSize com.example.lnnfilsh.ui.screens  fillMaxWidth com.example.lnnfilsh.ui.screens  height com.example.lnnfilsh.ui.screens  
isNotEmpty com.example.lnnfilsh.ui.screens  let com.example.lnnfilsh.ui.screens  offset com.example.lnnfilsh.ui.screens  padding com.example.lnnfilsh.ui.screens  painterResource com.example.lnnfilsh.ui.screens  
playBeepSound com.example.lnnfilsh.ui.screens  pointerInput com.example.lnnfilsh.ui.screens  provideDelegate com.example.lnnfilsh.ui.screens  rangeTo com.example.lnnfilsh.ui.screens  rememberScrollState com.example.lnnfilsh.ui.screens  size com.example.lnnfilsh.ui.screens  spacedBy com.example.lnnfilsh.ui.screens  tabIndicatorOffset com.example.lnnfilsh.ui.screens  take com.example.lnnfilsh.ui.screens  takeScreenshotAndShare com.example.lnnfilsh.ui.screens  verticalScroll com.example.lnnfilsh.ui.screens  width com.example.lnnfilsh.ui.screens  app 'com.example.lnnfilsh.ui.screens.android  Activity +com.example.lnnfilsh.ui.screens.android.app  
AccentBlue com.example.lnnfilsh.ui.theme  AccentGreen com.example.lnnfilsh.ui.theme  Activity com.example.lnnfilsh.ui.theme  	BlendMode com.example.lnnfilsh.ui.theme  Boolean com.example.lnnfilsh.ui.theme  BrightAccent com.example.lnnfilsh.ui.theme  Brush com.example.lnnfilsh.ui.theme  Build com.example.lnnfilsh.ui.theme  Canvas com.example.lnnfilsh.ui.theme  ChartLineColor com.example.lnnfilsh.ui.theme  Color com.example.lnnfilsh.ui.theme  ColorFilter com.example.lnnfilsh.ui.theme  
Composable com.example.lnnfilsh.ui.theme  ConnectedGreen com.example.lnnfilsh.ui.theme  ControlButtonBg com.example.lnnfilsh.ui.theme  ControlButtonTextColor com.example.lnnfilsh.ui.theme  ControlPanelBackground com.example.lnnfilsh.ui.theme  DarkBlue com.example.lnnfilsh.ui.theme  DarkColorScheme com.example.lnnfilsh.ui.theme  DarkTransparentBackground com.example.lnnfilsh.ui.theme  DepthScaleColor com.example.lnnfilsh.ui.theme  DisconnectedRed com.example.lnnfilsh.ui.theme  	DrawScope com.example.lnnfilsh.ui.theme  FastOutSlowInEasing com.example.lnnfilsh.ui.theme  FishData com.example.lnnfilsh.ui.theme  
FishIconColor com.example.lnnfilsh.ui.theme  FishIconOutlineColor com.example.lnnfilsh.ui.theme  
FishIconWhite com.example.lnnfilsh.ui.theme  FishIconYellow com.example.lnnfilsh.ui.theme  Float com.example.lnnfilsh.ui.theme  
FontFamily com.example.lnnfilsh.ui.theme  
FontWeight com.example.lnnfilsh.ui.theme  HighlightColor com.example.lnnfilsh.ui.theme  ImageBitmap com.example.lnnfilsh.ui.theme  	IntOffset com.example.lnnfilsh.ui.theme  IntSize com.example.lnnfilsh.ui.theme  LargeFishColor com.example.lnnfilsh.ui.theme  LightColorScheme com.example.lnnfilsh.ui.theme  LightTransparentBackground com.example.lnnfilsh.ui.theme  LinearEasing com.example.lnnfilsh.ui.theme  List com.example.lnnfilsh.ui.theme  
LnnfilshTheme com.example.lnnfilsh.ui.theme  MediumFishColor com.example.lnnfilsh.ui.theme  Modifier com.example.lnnfilsh.ui.theme  Offset com.example.lnnfilsh.ui.theme  PI com.example.lnnfilsh.ui.theme  Pair com.example.lnnfilsh.ui.theme  Path com.example.lnnfilsh.ui.theme  Pink40 com.example.lnnfilsh.ui.theme  Pink80 com.example.lnnfilsh.ui.theme  Purple40 com.example.lnnfilsh.ui.theme  Purple80 com.example.lnnfilsh.ui.theme  PurpleGrey40 com.example.lnnfilsh.ui.theme  PurpleGrey80 com.example.lnnfilsh.ui.theme  R com.example.lnnfilsh.ui.theme  
RepeatMode com.example.lnnfilsh.ui.theme  
RiverbedColor com.example.lnnfilsh.ui.theme  RiverbedDarkOrange com.example.lnnfilsh.ui.theme  RiverbedDeepOrange com.example.lnnfilsh.ui.theme  
RiverbedGreen com.example.lnnfilsh.ui.theme  RiverbedMidOrange com.example.lnnfilsh.ui.theme  RiverbedOrange com.example.lnnfilsh.ui.theme  RiverbedRed com.example.lnnfilsh.ui.theme  RiverbedSoilBrown com.example.lnnfilsh.ui.theme  RiverbedSurfaceGold com.example.lnnfilsh.ui.theme  RiverbedYellow com.example.lnnfilsh.ui.theme  Size com.example.lnnfilsh.ui.theme  SmallFishColor com.example.lnnfilsh.ui.theme  Stroke com.example.lnnfilsh.ui.theme  Suppress com.example.lnnfilsh.ui.theme  SurfaceDark com.example.lnnfilsh.ui.theme  SurfaceLight com.example.lnnfilsh.ui.theme  	TextStyle com.example.lnnfilsh.ui.theme  
Typography com.example.lnnfilsh.ui.theme  Unit com.example.lnnfilsh.ui.theme  WaterAbyssBlue com.example.lnnfilsh.ui.theme  	WaterBlue com.example.lnnfilsh.ui.theme  
WaterDeepBlue com.example.lnnfilsh.ui.theme  
WaterDeepNavy com.example.lnnfilsh.ui.theme  WaterDodgerBlue com.example.lnnfilsh.ui.theme  WaterLayerEndColor com.example.lnnfilsh.ui.theme  WaterLayerStartColor com.example.lnnfilsh.ui.theme  WaterLightBlue com.example.lnnfilsh.ui.theme  WaterMediumBlue com.example.lnnfilsh.ui.theme  WaterSkyBlue com.example.lnnfilsh.ui.theme  WaterSteelBlue com.example.lnnfilsh.ui.theme  WindowCompat com.example.lnnfilsh.ui.theme  abs com.example.lnnfilsh.ui.theme  androidx com.example.lnnfilsh.ui.theme  animateFloat com.example.lnnfilsh.ui.theme  clipToBounds com.example.lnnfilsh.ui.theme  coerceIn com.example.lnnfilsh.ui.theme  cos com.example.lnnfilsh.ui.theme  	emptyList com.example.lnnfilsh.ui.theme  fillMaxSize com.example.lnnfilsh.ui.theme  first com.example.lnnfilsh.ui.theme  forEach com.example.lnnfilsh.ui.theme  forEachIndexed com.example.lnnfilsh.ui.theme  getValue com.example.lnnfilsh.ui.theme  
imageResource com.example.lnnfilsh.ui.theme  infiniteRepeatable com.example.lnnfilsh.ui.theme  
isNotEmpty com.example.lnnfilsh.ui.theme  listOf com.example.lnnfilsh.ui.theme  	maxOrNull com.example.lnnfilsh.ui.theme  minOf com.example.lnnfilsh.ui.theme  
mutableListOf com.example.lnnfilsh.ui.theme  provideDelegate com.example.lnnfilsh.ui.theme  radialGradient com.example.lnnfilsh.ui.theme  remember com.example.lnnfilsh.ui.theme  rememberInfiniteTransition com.example.lnnfilsh.ui.theme  sin com.example.lnnfilsh.ui.theme  
sweepGradient com.example.lnnfilsh.ui.theme  tint com.example.lnnfilsh.ui.theme  tween com.example.lnnfilsh.ui.theme  with com.example.lnnfilsh.ui.theme  compose &com.example.lnnfilsh.ui.theme.androidx  ui .com.example.lnnfilsh.ui.theme.androidx.compose  text 1com.example.lnnfilsh.ui.theme.androidx.compose.ui  TextMeasurer 6com.example.lnnfilsh.ui.theme.androidx.compose.ui.text  Bitmap com.example.lnnfilsh.utils  Build com.example.lnnfilsh.utils  Canvas com.example.lnnfilsh.utils  
ContentValues com.example.lnnfilsh.utils  Context com.example.lnnfilsh.utils  Date com.example.lnnfilsh.utils  Environment com.example.lnnfilsh.utils  	Exception com.example.lnnfilsh.utils  File com.example.lnnfilsh.utils  FileOutputStream com.example.lnnfilsh.utils  FileProvider com.example.lnnfilsh.utils  IllegalStateException com.example.lnnfilsh.utils  Intent com.example.lnnfilsh.utils  Locale com.example.lnnfilsh.utils  Log com.example.lnnfilsh.utils  
MediaStore com.example.lnnfilsh.utils  ScreenshotUtil com.example.lnnfilsh.utils  SimpleDateFormat com.example.lnnfilsh.utils  String com.example.lnnfilsh.utils  Uri com.example.lnnfilsh.utils  View com.example.lnnfilsh.utils  apply com.example.lnnfilsh.utils  use com.example.lnnfilsh.utils  Bitmap )com.example.lnnfilsh.utils.ScreenshotUtil  Build )com.example.lnnfilsh.utils.ScreenshotUtil  Canvas )com.example.lnnfilsh.utils.ScreenshotUtil  
ContentValues )com.example.lnnfilsh.utils.ScreenshotUtil  Date )com.example.lnnfilsh.utils.ScreenshotUtil  Environment )com.example.lnnfilsh.utils.ScreenshotUtil  File )com.example.lnnfilsh.utils.ScreenshotUtil  FileOutputStream )com.example.lnnfilsh.utils.ScreenshotUtil  FileProvider )com.example.lnnfilsh.utils.ScreenshotUtil  IllegalStateException )com.example.lnnfilsh.utils.ScreenshotUtil  Intent )com.example.lnnfilsh.utils.ScreenshotUtil  Locale )com.example.lnnfilsh.utils.ScreenshotUtil  Log )com.example.lnnfilsh.utils.ScreenshotUtil  
MediaStore )com.example.lnnfilsh.utils.ScreenshotUtil  SimpleDateFormat )com.example.lnnfilsh.utils.ScreenshotUtil  TAG )com.example.lnnfilsh.utils.ScreenshotUtil  apply )com.example.lnnfilsh.utils.ScreenshotUtil  saveAndShareBitmap )com.example.lnnfilsh.utils.ScreenshotUtil  saveImageToExternalStorage )com.example.lnnfilsh.utils.ScreenshotUtil  saveImageToMediaStore )com.example.lnnfilsh.utils.ScreenshotUtil  
shareImage )com.example.lnnfilsh.utils.ScreenshotUtil  takeScreenshot )com.example.lnnfilsh.utils.ScreenshotUtil  takeScreenshotAndShare )com.example.lnnfilsh.utils.ScreenshotUtil  use )com.example.lnnfilsh.utils.ScreenshotUtil  Application com.example.lnnfilsh.viewmodel  Boolean com.example.lnnfilsh.viewmodel  Class com.example.lnnfilsh.viewmodel  Command com.example.lnnfilsh.viewmodel  Context com.example.lnnfilsh.viewmodel  Dispatchers com.example.lnnfilsh.viewmodel  	Exception com.example.lnnfilsh.viewmodel  FishData com.example.lnnfilsh.viewmodel  FishDetectorViewModel com.example.lnnfilsh.viewmodel  Float com.example.lnnfilsh.viewmodel  IllegalArgumentException com.example.lnnfilsh.viewmodel  Int com.example.lnnfilsh.viewmodel  List com.example.lnnfilsh.viewmodel  Log com.example.lnnfilsh.viewmodel  MutableStateFlow com.example.lnnfilsh.viewmodel  Random com.example.lnnfilsh.viewmodel  SensorDataType com.example.lnnfilsh.viewmodel  	StateFlow com.example.lnnfilsh.viewmodel  String com.example.lnnfilsh.viewmodel  Suppress com.example.lnnfilsh.viewmodel  System com.example.lnnfilsh.viewmodel  T com.example.lnnfilsh.viewmodel  	TcpClient com.example.lnnfilsh.viewmodel  	ViewModel com.example.lnnfilsh.viewmodel  ViewModelFactory com.example.lnnfilsh.viewmodel  ViewModelProvider com.example.lnnfilsh.viewmodel  _batteryPercentage com.example.lnnfilsh.viewmodel  _connectionStatus com.example.lnnfilsh.viewmodel  
_currentDepth com.example.lnnfilsh.viewmodel  
_depthHistory com.example.lnnfilsh.viewmodel  
_errorMessage com.example.lnnfilsh.viewmodel  	_fishData com.example.lnnfilsh.viewmodel  
_isCollecting com.example.lnnfilsh.viewmodel  
_ledStatus com.example.lnnfilsh.viewmodel  _samplingInterval com.example.lnnfilsh.viewmodel  _sensitivity com.example.lnnfilsh.viewmodel  _ultrasonicData com.example.lnnfilsh.viewmodel  _waterTemperature com.example.lnnfilsh.viewmodel  abs com.example.lnnfilsh.viewmodel  apply com.example.lnnfilsh.viewmodel  cancel com.example.lnnfilsh.viewmodel  checkAlarmTrigger com.example.lnnfilsh.viewmodel  coerceIn com.example.lnnfilsh.viewmodel  
component1 com.example.lnnfilsh.viewmodel  
component2 com.example.lnnfilsh.viewmodel  delay com.example.lnnfilsh.viewmodel  	emptyList com.example.lnnfilsh.viewmodel  forEach com.example.lnnfilsh.viewmodel  isFirstDataAfterConnection com.example.lnnfilsh.viewmodel  
isNotEmpty com.example.lnnfilsh.viewmodel  java com.example.lnnfilsh.viewmodel  kotlin com.example.lnnfilsh.viewmodel  launch com.example.lnnfilsh.viewmodel  listOf com.example.lnnfilsh.viewmodel  
mutableListOf com.example.lnnfilsh.viewmodel  	nextFloat com.example.lnnfilsh.viewmodel  nextInt com.example.lnnfilsh.viewmodel  
plusAssign com.example.lnnfilsh.viewmodel  simulateFishData com.example.lnnfilsh.viewmodel  simulateUltrasonicData com.example.lnnfilsh.viewmodel  sin com.example.lnnfilsh.viewmodel  startCollecting com.example.lnnfilsh.viewmodel  
startsWith com.example.lnnfilsh.viewmodel  	tcpClient com.example.lnnfilsh.viewmodel  to com.example.lnnfilsh.viewmodel  
toMutableList com.example.lnnfilsh.viewmodel  until com.example.lnnfilsh.viewmodel  withContext com.example.lnnfilsh.viewmodel  Command 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  Context 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  Dispatchers 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  FishData 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  Log 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  MutableStateFlow 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  Random 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  SensorDataType 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  System 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	TcpClient 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _alarmActive 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _batteryPercentage 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _connectionStatus 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
_currentDepth 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
_depthHistory 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _depthThreshold 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
_displayStyle 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
_errorMessage 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	_fishData 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
_isCollecting 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _isMovingBackward 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _isMovingForward 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _isTurningLeft 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _isTurningRight 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
_ledStatus 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _samplingInterval 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _sensitivity 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _showDirectionButtons 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _ultrasonicData 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _vibrationEnabled 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  _waterTemperature 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  abs 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  apply 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  batteryPercentage 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  cancel 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  checkAlarmTrigger 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  clearErrorMessage 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  coerceIn 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
component1 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
component2 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  connect 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  connectionStatus 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  currentDepth 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  delay 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  depthHistory 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
disconnect 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  displayStyle 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	emptyList 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  errorMessage 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  fishData 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  invoke 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  isCollecting 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  isFirstDataAfterConnection 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  isMovingBackward 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  isMovingForward 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
isNotEmpty 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
isTurningLeft 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  isTurningRight 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  kotlin 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  launch 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	ledStatus 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  listOf 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  moveBackward 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  moveForward 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
mutableListOf 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	nextFloat 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  nextInt 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
plusAssign 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  resetDevice 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  samplingInterval 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  sensitivity 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  sharedPreferences 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  showDirectionButtons 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  simulateFishData 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  simulateUltrasonicData 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  sin 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  startCollecting 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
startsWith 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	tcpClient 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  to 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  
toMutableList 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	toggleLed 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  turnLeft 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  	turnRight 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  ultrasonicData 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  until 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel   updateDirectionButtonsVisibility 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  updateDisplayStyle 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  updateErrorMessage 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  updateSamplingInterval 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  updateSensitivity 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  updateVibrationEnabled 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  vibrationEnabled 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  viewModelScope 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  waterTemperature 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  withContext 4com.example.lnnfilsh.viewmodel.FishDetectorViewModel  FishDetectorViewModel /com.example.lnnfilsh.viewmodel.ViewModelFactory  IllegalArgumentException /com.example.lnnfilsh.viewmodel.ViewModelFactory  application /com.example.lnnfilsh.viewmodel.ViewModelFactory  java /com.example.lnnfilsh.viewmodel.ViewModelFactory  Factory 0com.example.lnnfilsh.viewmodel.ViewModelProvider  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  SerializedName com.google.gson.annotations  File java.io  FileOutputStream java.io  InputStream java.io  OutputStream java.io  use java.io.FileOutputStream  close java.io.InputStream  read java.io.InputStream  close java.io.OutputStream  flush java.io.OutputStream  use java.io.OutputStream  write java.io.OutputStream  Class 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  Runnable 	java.lang  isAssignableFrom java.lang.Class  name java.lang.Class  <SAM-CONSTRUCTOR> java.lang.Runnable  	arraycopy java.lang.System  currentTimeMillis java.lang.System  UncaughtExceptionHandler java.lang.Thread  "getDefaultUncaughtExceptionHandler java.lang.Thread  "setDefaultUncaughtExceptionHandler java.lang.Thread  sleep java.lang.Thread  <SAM-CONSTRUCTOR> )java.lang.Thread.UncaughtExceptionHandler  uncaughtException )java.lang.Thread.UncaughtExceptionHandler  
BigDecimal 	java.math  
BigInteger 	java.math  InetSocketAddress java.net  Socket java.net  close java.net.Socket  connect java.net.Socket  getInputStream java.net.Socket  getOutputStream java.net.Socket  	keepAlive java.net.Socket  	soTimeout java.net.Socket  
tcpNoDelay java.net.Socket  
ByteBuffer java.nio  	ByteOrder java.nio  position java.nio.Buffer  allocate java.nio.ByteBuffer  array java.nio.ByteBuffer  order java.nio.ByteBuffer  position java.nio.ByteBuffer  put java.nio.ByteBuffer  short java.nio.ByteBuffer  wrap java.nio.ByteBuffer  
LITTLE_ENDIAN java.nio.ByteOrder  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Date 	java.util  Locale 	java.util  
getDefault java.util.Locale  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  Number kotlin  Pair kotlin  
ShortArray kotlin  String kotlin  Suppress kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  apply kotlin  byteArrayOf kotlin  floatArrayOf kotlin  let kotlin  plus kotlin  repeat kotlin  run kotlin  to kotlin  use kotlin  with kotlin  	javaClass 
kotlin.Any  not kotlin.Boolean  and kotlin.Byte  	compareTo kotlin.Byte  toInt kotlin.Byte  
contentEquals kotlin.ByteArray  contentHashCode kotlin.ByteArray  copyOf kotlin.ByteArray  copyOfRange kotlin.ByteArray  get kotlin.ByteArray  
isNotEmpty kotlin.ByteArray  last kotlin.ByteArray  plus kotlin.ByteArray  set kotlin.ByteArray  size kotlin.ByteArray  div 
kotlin.Double  minus 
kotlin.Double  sp 
kotlin.Double  times 
kotlin.Double  toFloat 
kotlin.Double  message kotlin.Exception  coerceIn kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  
plusAssign kotlin.Float  rangeTo kotlin.Float  times kotlin.Float  to kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  get kotlin.FloatArray  indices kotlin.FloatArray  joinToString kotlin.FloatArray  set kotlin.FloatArray  invoke kotlin.Function0  and 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  dp 
kotlin.Int  inc 
kotlin.Int  invoke 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toByte 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  until 
kotlin.Int  ushr 
kotlin.Int  xor 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  toByte 
kotlin.Number  
component1 kotlin.Pair  
component2 kotlin.Pair  toInt kotlin.Short  	Companion 
kotlin.String  endsWith 
kotlin.String  format 
kotlin.String  invoke 
kotlin.String  length 
kotlin.String  let 
kotlin.String  
startsWith 
kotlin.String  take 
kotlin.String  to 
kotlin.String  format kotlin.String.Companion  invoke kotlin.String.Companion  message kotlin.Throwable  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  Set kotlin.collections  any kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  
contentEquals kotlin.collections  contentHashCode kotlin.collections  copyOf kotlin.collections  copyOfRange kotlin.collections  copyOfRangeInline kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  first kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  	getOrNull kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  last kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  	mapValues kotlin.collections  	maxOrNull kotlin.collections  min kotlin.collections  minOf kotlin.collections  minOfOrNull kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  take kotlin.collections  takeLast kotlin.collections  toByteArray kotlin.collections  
toMutableList kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  first kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  	getOrNull kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  	maxOrNull kotlin.collections.List  size kotlin.collections.List  takeLast kotlin.collections.List  
toMutableList kotlin.collections.List  Entry kotlin.collections.Map  containsKey kotlin.collections.Map  get kotlin.collections.Map  	mapValues kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  apply kotlin.collections.MutableList  first kotlin.collections.MutableList  get kotlin.collections.MutableList  	getOrNull kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  last kotlin.collections.MutableList  minOfOrNull kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  set kotlin.collections.MutableMap  minOf kotlin.comparisons  SuspendFunction1 kotlin.coroutines  and kotlin.experimental  or kotlin.experimental  endsWith 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  java 
kotlin.jvm  	javaClass 
kotlin.jvm  kotlin 
kotlin.jvm  	BlendMode kotlin.math  Brush kotlin.math  Canvas kotlin.math  Color kotlin.math  ColorFilter kotlin.math  
Composable kotlin.math  	DrawScope kotlin.math  FastOutSlowInEasing kotlin.math  FishData kotlin.math  Float kotlin.math  
FontWeight kotlin.math  ImageBitmap kotlin.math  	IntOffset kotlin.math  IntSize kotlin.math  LinearEasing kotlin.math  List kotlin.math  Modifier kotlin.math  Offset kotlin.math  PI kotlin.math  Pair kotlin.math  Path kotlin.math  R kotlin.math  
RepeatMode kotlin.math  Size kotlin.math  Stroke kotlin.math  	TextStyle kotlin.math  abs kotlin.math  androidx kotlin.math  animateFloat kotlin.math  clipToBounds kotlin.math  coerceIn kotlin.math  cos kotlin.math  	emptyList kotlin.math  fillMaxSize kotlin.math  first kotlin.math  forEach kotlin.math  forEachIndexed kotlin.math  getValue kotlin.math  
imageResource kotlin.math  infiniteRepeatable kotlin.math  
isNotEmpty kotlin.math  listOf kotlin.math  	maxOrNull kotlin.math  min kotlin.math  minOf kotlin.math  
mutableListOf kotlin.math  provideDelegate kotlin.math  radialGradient kotlin.math  remember kotlin.math  rememberInfiniteTransition kotlin.math  sin kotlin.math  
sweepGradient kotlin.math  tint kotlin.math  tween kotlin.math  with kotlin.math  compose kotlin.math.androidx  ui kotlin.math.androidx.compose  text kotlin.math.androidx.compose.ui  TextMeasurer $kotlin.math.androidx.compose.ui.text  Random 
kotlin.random  Default kotlin.random.Random  	nextFloat kotlin.random.Random  nextInt kotlin.random.Random  	nextFloat kotlin.random.Random.Default  nextInt kotlin.random.Random.Default  CharProgression 
kotlin.ranges  	CharRange 
kotlin.ranges  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  	UIntRange 
kotlin.ranges  ULongProgression 
kotlin.ranges  
ULongRange 
kotlin.ranges  coerceIn 
kotlin.ranges  first 
kotlin.ranges  last 
kotlin.ranges  rangeTo 
kotlin.ranges  step 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  step kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  step kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  joinToString kotlin.sequences  last kotlin.sequences  	maxOrNull kotlin.sequences  min kotlin.sequences  minOf kotlin.sequences  minOfOrNull kotlin.sequences  plus kotlin.sequences  take kotlin.sequences  
toMutableList kotlin.sequences  String kotlin.text  any kotlin.text  
contentEquals kotlin.text  endsWith kotlin.text  first kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  	getOrNull kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text  last kotlin.text  	maxOrNull kotlin.text  min kotlin.text  minOf kotlin.text  minOfOrNull kotlin.text  plus kotlin.text  repeat kotlin.text  set kotlin.text  
startsWith kotlin.text  take kotlin.text  takeLast kotlin.text  toByteArray kotlin.text  
toMutableList kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  cancel kotlinx.coroutines  delay kotlinx.coroutines  isActive kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  withTimeoutOrNull kotlinx.coroutines  	ByteArray !kotlinx.coroutines.CoroutineScope  CMD_MOVE_BACKWARD !kotlinx.coroutines.CoroutineScope  CMD_MOVE_FORWARD !kotlinx.coroutines.CoroutineScope  CMD_RESET_DEVICE !kotlinx.coroutines.CoroutineScope  CMD_START_COLLECTING !kotlinx.coroutines.CoroutineScope  CMD_STOP !kotlinx.coroutines.CoroutineScope  CMD_STOP_COLLECTING !kotlinx.coroutines.CoroutineScope  
CMD_TURN_LEFT !kotlinx.coroutines.CoroutineScope  CMD_TURN_RIGHT !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  FishData !kotlinx.coroutines.CoroutineScope  Handler !kotlinx.coroutines.CoroutineScope  InetSocketAddress !kotlinx.coroutines.CoroutineScope  	Lifecycle !kotlinx.coroutines.CoroutineScope  
LnnfilshTheme !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Looper !kotlinx.coroutines.CoroutineScope  
MainScreen !kotlinx.coroutines.CoroutineScope  
MaterialTheme !kotlinx.coroutines.CoroutineScope  Modifier !kotlinx.coroutines.CoroutineScope  Random !kotlinx.coroutines.CoroutineScope  SensorDataType !kotlinx.coroutines.CoroutineScope  Socket !kotlinx.coroutines.CoroutineScope  Surface !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Thread !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  _batteryPercentage !kotlinx.coroutines.CoroutineScope  _commandResponseFlow !kotlinx.coroutines.CoroutineScope  _connectionStatus !kotlinx.coroutines.CoroutineScope  _connectionStatusFlow !kotlinx.coroutines.CoroutineScope  
_currentDepth !kotlinx.coroutines.CoroutineScope  
_depthHistory !kotlinx.coroutines.CoroutineScope  _deviceStatusFlow !kotlinx.coroutines.CoroutineScope  
_errorMessage !kotlinx.coroutines.CoroutineScope  	_fishData !kotlinx.coroutines.CoroutineScope  
_isCollecting !kotlinx.coroutines.CoroutineScope  
_ledStatus !kotlinx.coroutines.CoroutineScope  _samplingInterval !kotlinx.coroutines.CoroutineScope  _sensitivity !kotlinx.coroutines.CoroutineScope  _sensorDataFlow !kotlinx.coroutines.CoroutineScope  _ultrasonicData !kotlinx.coroutines.CoroutineScope  _waterTemperature !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  checkAlarmTrigger !kotlinx.coroutines.CoroutineScope  closeConnection !kotlinx.coroutines.CoroutineScope  coerceIn !kotlinx.coroutines.CoroutineScope  
component1 !kotlinx.coroutines.CoroutineScope  
component2 !kotlinx.coroutines.CoroutineScope  connect !kotlinx.coroutines.CoroutineScope  connectionTimeout !kotlinx.coroutines.CoroutineScope  copyOfRange !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  fillMaxSize !kotlinx.coroutines.CoroutineScope  gson !kotlinx.coroutines.CoroutineScope  handleConnectionError !kotlinx.coroutines.CoroutineScope  inputStream !kotlinx.coroutines.CoroutineScope  isActive !kotlinx.coroutines.CoroutineScope  isConnected !kotlinx.coroutines.CoroutineScope  isFirstDataAfterConnection !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  minOf !kotlinx.coroutines.CoroutineScope  
mutableListOf !kotlinx.coroutines.CoroutineScope  	nextFloat !kotlinx.coroutines.CoroutineScope  outputStream !kotlinx.coroutines.CoroutineScope  
playBeepSound !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  processDataFrames !kotlinx.coroutines.CoroutineScope  reconnectAttempts !kotlinx.coroutines.CoroutineScope  repeatOnLifecycle !kotlinx.coroutines.CoroutineScope  run !kotlinx.coroutines.CoroutineScope  sendBinaryCommand !kotlinx.coroutines.CoroutineScope  
setContent !kotlinx.coroutines.CoroutineScope  
setParameters !kotlinx.coroutines.CoroutineScope  simulateFishData !kotlinx.coroutines.CoroutineScope  simulateUltrasonicData !kotlinx.coroutines.CoroutineScope  socket !kotlinx.coroutines.CoroutineScope  startCollecting !kotlinx.coroutines.CoroutineScope  startHeartbeat !kotlinx.coroutines.CoroutineScope  startReceiving !kotlinx.coroutines.CoroutineScope  
startsWith !kotlinx.coroutines.CoroutineScope  take !kotlinx.coroutines.CoroutineScope  	tcpClient !kotlinx.coroutines.CoroutineScope  toByteArray !kotlinx.coroutines.CoroutineScope  
toMutableList !kotlinx.coroutines.CoroutineScope  	toggleLed !kotlinx.coroutines.CoroutineScope  until !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  withTimeoutOrNull !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  
FlowCollector kotlinx.coroutines.flow  MutableSharedFlow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  
SharedFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  emit )kotlinx.coroutines.flow.MutableSharedFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collect "kotlinx.coroutines.flow.SharedFlow  collectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  