package com.example.lnnfilsh.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.outlined.PlayArrow
import androidx.compose.material.icons.outlined.Stop
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lnnfilsh.R
import com.example.lnnfilsh.ui.theme.AccentBlue
import com.example.lnnfilsh.ui.theme.AccentGreen
import com.example.lnnfilsh.ui.theme.BrightAccent
import com.example.lnnfilsh.ui.theme.DarkTransparentBackground
import com.example.lnnfilsh.ui.theme.DisconnectedRed

@Composable
fun ControlPanel(
    sensitivity: Float,
    onSensitivityChange: (Float) -> Unit,
    isCollecting: Boolean,
    onStartStopClick: () -> Unit,
    isConnected: Boolean,
    onConnectClick: () -> Unit,
    onScreenshotClick: () -> Unit,
    onResetClick: () -> Unit,
    samplingInterval: Float,
    onSamplingIntervalChange: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = DarkTransparentBackground
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        )
    ) {
        Column(
            modifier = Modifier
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 灵敏度控制
            Text(
                text = "灵敏度: ${(sensitivity * 100).toInt()}%",
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold
            )
            
            Slider(
                value = sensitivity,
                onValueChange = onSensitivityChange,
                valueRange = 0f..1f,
                modifier = Modifier.fillMaxWidth(),
                colors = SliderDefaults.colors(
                    thumbColor = AccentBlue,
                    activeTrackColor = AccentBlue,
                    inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                )
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 采样间隔控制
            Text(
                text = "采样间隔: ${samplingInterval.toInt()}ms",
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold
            )
            
            Slider(
                value = samplingInterval,
                onValueChange = onSamplingIntervalChange,
                valueRange = 100f..1000f,
                steps = 8,
                modifier = Modifier.fillMaxWidth(),
                colors = SliderDefaults.colors(
                    thumbColor = AccentGreen,
                    activeTrackColor = AccentGreen,
                    inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                )
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 操作按钮 - 以半透明圆形方式呈现
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 启动/停止按钮
                CircleIconButton(
                    onClick = onStartStopClick,
                    icon = if (isCollecting) Icons.Outlined.Stop else Icons.Outlined.PlayArrow,
                    contentDescription = if (isCollecting) "停止" else "开始",
                    backgroundColor = if (isCollecting) DisconnectedRed else AccentGreen,
                    size = 50.dp
                )
                
                // 连接按钮
                CircleIconButton(
                    onClick = onConnectClick,
                    painter = painterResource(id = if (isConnected) R.drawable.ic_wifi else R.drawable.ic_bluetooth),
                    contentDescription = if (isConnected) "已连接" else "连接",
                    backgroundColor = if (isConnected) BrightAccent else Color.Gray.copy(alpha = 0.8f),
                    size = 50.dp
                )
                
                // 截图按钮
                CircleIconButton(
                    onClick = onScreenshotClick,
                    painter = painterResource(id = R.drawable.ic_screenshot),
                    contentDescription = "截图",
                    backgroundColor = AccentBlue.copy(alpha = 0.8f),
                    size = 50.dp
                )
                
                // 重置按钮
                CircleIconButton(
                    onClick = onResetClick,
                    icon = Icons.Default.Refresh,
                    contentDescription = "重置",
                    backgroundColor = Color.White.copy(alpha = 0.3f),
                    size = 50.dp
                )
            }
        }
    }
} 