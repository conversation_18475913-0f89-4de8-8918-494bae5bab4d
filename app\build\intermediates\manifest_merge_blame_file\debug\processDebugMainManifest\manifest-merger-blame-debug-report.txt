1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.lnnfilsh"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:6:5-76
12-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:6:22-73
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:8:5-66
14-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission
15-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:12:9-35
21
22    <permission
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.example.lnnfilsh.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.example.lnnfilsh.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:14:5-47:19
29        android:allowBackup="true"
29-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:15:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:16:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="true"
34        android:fullBackupContent="@xml/backup_rules"
34-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:17:9-54
35        android:icon="@mipmap/ic_launcher"
35-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:18:9-43
36        android:label="@string/app_name"
36-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:19:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:20:9-54
38        android:supportsRtl="true"
38-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:21:9-35
39        android:testOnly="true"
40        android:theme="@style/Theme.Lnnfilsh" >
40-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:22:9-46
41        <activity
41-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:24:9-35:20
42            android:name="com.example.lnnfilsh.MainActivity"
42-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:25:13-41
43            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden|navigation"
43-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:27:13-98
44            android:exported="true"
44-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:26:13-36
45            android:screenOrientation="landscape"
45-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:28:13-50
46            android:theme="@style/Theme.Lnnfilsh" >
46-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:29:13-50
47            <intent-filter>
47-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:30:13-34:29
48                <action android:name="android.intent.action.MAIN" />
48-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:31:17-69
48-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:31:25-66
49
50                <category android:name="android.intent.category.LAUNCHER" />
50-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:33:17-77
50-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:33:27-74
51            </intent-filter>
52        </activity>
53
54        <!-- FileProvider配置 -->
55        <provider
56            android:name="androidx.core.content.FileProvider"
56-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:39:13-62
57            android:authorities="com.example.lnnfilsh.fileprovider"
57-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:40:13-64
58            android:exported="false"
58-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:41:13-37
59            android:grantUriPermissions="true" >
59-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:42:13-47
60            <meta-data
60-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:43:13-45:54
61                android:name="android.support.FILE_PROVIDER_PATHS"
61-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:44:17-67
62                android:resource="@xml/file_paths" />
62-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:45:17-51
63        </provider>
64
65        <activity
65-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
66            android:name="androidx.compose.ui.tooling.PreviewActivity"
66-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
67            android:exported="true" />
67-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
68
69        <provider
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
70            android:name="androidx.startup.InitializationProvider"
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
71            android:authorities="com.example.lnnfilsh.androidx-startup"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
72            android:exported="false" >
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
73            <meta-data
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.emoji2.text.EmojiCompatInitializer"
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
75                android:value="androidx.startup" />
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
78                android:value="androidx.startup" />
78-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
79            <meta-data
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
80                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
81                android:value="androidx.startup" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
82        </provider>
83
84        <activity
84-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
85            android:name="androidx.activity.ComponentActivity"
85-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
86            android:exported="true" />
86-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
87
88        <receiver
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
89            android:name="androidx.profileinstaller.ProfileInstallReceiver"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
90            android:directBootAware="false"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
91            android:enabled="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
92            android:exported="true"
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
93            android:permission="android.permission.DUMP" >
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
95                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
98                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
101                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
104                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
105            </intent-filter>
106        </receiver>
107    </application>
108
109</manifest>
