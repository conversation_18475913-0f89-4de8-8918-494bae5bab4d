1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.lnnfilsh"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:6:5-76
12-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:6:22-73
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:8:5-66
14-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission
15-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:12:9-35
21
22    <permission
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.example.lnnfilsh.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.example.lnnfilsh.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:14:5-47:19
29        android:allowBackup="true"
29-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:15:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:16:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="true"
34        android:fullBackupContent="@xml/backup_rules"
34-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:17:9-54
35        android:icon="@mipmap/ic_launcher"
35-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:18:9-43
36        android:label="@string/app_name"
36-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:19:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:20:9-54
38        android:supportsRtl="true"
38-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:21:9-35
39        android:theme="@style/Theme.Lnnfilsh" >
39-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:22:9-46
40        <activity
40-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:24:9-35:20
41            android:name="com.example.lnnfilsh.MainActivity"
41-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:25:13-41
42            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden|navigation"
42-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:27:13-98
43            android:exported="true"
43-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:26:13-36
44            android:screenOrientation="fullSensor"
44-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:28:13-51
45            android:theme="@style/Theme.Lnnfilsh" >
45-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:29:13-50
46            <intent-filter>
46-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:30:13-34:29
47                <action android:name="android.intent.action.MAIN" />
47-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:31:17-69
47-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:31:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:33:17-77
49-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:33:27-74
50            </intent-filter>
51        </activity>
52
53        <!-- FileProvider配置 -->
54        <provider
55            android:name="androidx.core.content.FileProvider"
55-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:39:13-62
56            android:authorities="com.example.lnnfilsh.fileprovider"
56-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:40:13-64
57            android:exported="false"
57-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:41:13-37
58            android:grantUriPermissions="true" >
58-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:42:13-47
59            <meta-data
59-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:43:13-45:54
60                android:name="android.support.FILE_PROVIDER_PATHS"
60-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:44:17-67
61                android:resource="@xml/file_paths" />
61-->D:\anAPP\filsh\app\src\main\AndroidManifest.xml:45:17-51
62        </provider>
63
64        <activity
64-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
65            android:name="androidx.compose.ui.tooling.PreviewActivity"
65-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
66            android:exported="true" />
66-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
67
68        <provider
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
69            android:name="androidx.startup.InitializationProvider"
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
70            android:authorities="com.example.lnnfilsh.androidx-startup"
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
71            android:exported="false" >
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
72            <meta-data
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.emoji2.text.EmojiCompatInitializer"
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
74                android:value="androidx.startup" />
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
75            <meta-data
75-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
76                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
76-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
77                android:value="androidx.startup" />
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33fad0e40425b27af7d6ac53a8cc5138\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
78            <meta-data
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
79                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
80                android:value="androidx.startup" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
81        </provider>
82
83        <activity
83-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
84            android:name="androidx.activity.ComponentActivity"
84-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
85            android:exported="true" />
85-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
86
87        <receiver
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
88            android:name="androidx.profileinstaller.ProfileInstallReceiver"
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
89            android:directBootAware="false"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
90            android:enabled="true"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
91            android:exported="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
92            android:permission="android.permission.DUMP" >
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
94                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
97                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
100                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
103                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
104            </intent-filter>
105        </receiver>
106    </application>
107
108</manifest>
