package com.example.lnnfilsh.model

import com.google.gson.annotations.SerializedName

// 传感器数据类型
sealed class SensorDataType {
    object DEPTH : SensorDataType() // 0x01 深度数据
    object FISH : SensorDataType() // 0x03 鱼群数据
    object ULTRASONIC : SensorDataType() // 0x05 超声波数据
    object UltrasonicDistance : SensorDataType() // 兼容旧版本
    object Temperature : SensorDataType() // 兼容旧版本
    object DeviceStatus : SensorDataType() // 兼容旧版本
    object CommandResponse : SensorDataType() // 兼容旧版本
}

// 传感器数据
data class SensorData(
    val type: SensorDataType,
    val timestamp: Long = System.currentTimeMillis(),
    val values: Map<String, Float> = emptyMap(),
    val value: Float = 0f // 兼容旧版本
)

// 设备状态
data class DeviceStatus(
    val batteryPercentage: Int,
    val isCollecting: Boolean = false,
    val ledStatus: Boolean = false
)

// 鱼群数据
data class FishData(
    val depth: Float,
    val size: Int, // 1-小 2-中 3-大
    val confidence: Float,
    val x: Float = 0f, // 用于UI显示的坐标
    val y: Float = 0f  // 用于UI显示的坐标
)

// 超声波探测数据
data class UltrasonicData(
    val points: List<Float>, // 多个深度点的数据
    val timestamp: Long = System.currentTimeMillis()
)

enum class FishSize {
    SMALL, MEDIUM, LARGE
}

// 上位机指令
data class Command(
    val type: String,
    val params: Map<String, Any>? = null
) {
    companion object {
        val MOVE_FORWARD = Command("moveForward")
        val MOVE_BACKWARD = Command("moveBackward")
        val TURN_LEFT = Command("turnLeft")
        val TURN_RIGHT = Command("turnRight")
        val STOP = Command("stop")
    }
}

// 下位机响应
data class CommandResponse(
    val success: Boolean = true,
    val commandType: Int = 0,
    val message: String = "",
    @SerializedName("ack") val acknowledgement: String? = null, // 兼容旧版本
    @SerializedName("error") val errorCode: String? = null // 兼容旧版本
)

// 通信错误码
object ErrorCode {
    const val CHECKSUM_FAILURE = "0x01"
    const val INVALID_COMMAND = "0x02"
    const val SENSOR_TIMEOUT = "0x03"
}

// 数据帧结构
data class DataFrame(
    val header: Int = 0xAABB,
    val type: Int,
    val dataLength: Int,
    val data: ByteArray,
    val checksum: ByteArray
) {
    // 重写equals和hashCode，因为使用了ByteArray
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as DataFrame

        if (header != other.header) return false
        if (type != other.type) return false
        if (dataLength != other.dataLength) return false
        if (!data.contentEquals(other.data)) return false
        return checksum.contentEquals(other.checksum)
    }

    override fun hashCode(): Int {
        var result = header
        result = 31 * result + type
        result = 31 * result + dataLength
        result = 31 * result + data.contentHashCode()
        result = 31 * result + checksum.contentHashCode()
        return result
    }
} 