package com.example.lnnfilsh.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lnnfilsh.R
import com.example.lnnfilsh.ui.theme.AccentBlue
import com.example.lnnfilsh.ui.theme.AccentGreen
import com.example.lnnfilsh.ui.theme.BrightAccent
import com.example.lnnfilsh.ui.theme.ConnectedGreen
import com.example.lnnfilsh.ui.theme.ControlPanelBackground
import com.example.lnnfilsh.ui.theme.DarkTransparentBackground
import com.example.lnnfilsh.ui.theme.DisconnectedRed
import com.example.lnnfilsh.ui.theme.FishIconYellow

@Composable
fun FloatingStatusPanel(
    currentDepth: Float,
    waterTemperature: Float,
    batteryPercentage: Int,
    isConnected: Boolean,
    ledStatus: Boolean,
    onLedToggle: () -> Unit,
    onScreenshotClick: () -> Unit,
    onSettingsClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = ControlPanelBackground.copy(alpha = 0.85f)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧：深度显示（主要信息）
            Column(
                horizontalAlignment = Alignment.Start
            ) {
                // 深度标签
                Text(
                    text = "深度",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.7f)
                )
                
                // 深度数值
                Text(
                    text = String.format("%.1fm", currentDepth/100),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = AccentBlue
                )
            }
            
            // 中间：温度显示
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 温度标签
                Text(
                    text = "水温",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.7f)
                )
                
                // 温度数值
                Text(
                    text = String.format("%.1f°C", waterTemperature),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = FishIconYellow
                )
            }
            
            // 右侧：状态指示区域
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 电池状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(end = 12.dp)
                ) {
                    // 电池图标 - 根据电池百分比显示不同的图标
                    val batteryIconId = when {
                        batteryPercentage <= 15 -> R.drawable.ic_battery_low
                        batteryPercentage <= 30 -> R.drawable.ic_battery_25
                        batteryPercentage <= 60 -> R.drawable.ic_battery_50
                        batteryPercentage <= 85 -> R.drawable.ic_battery_75
                        else -> R.drawable.ic_battery_full
                    }
                    
                    val batteryColor = when {
                        batteryPercentage <= 15 -> DisconnectedRed
                        batteryPercentage <= 30 -> Color(0xFFFF9800) // 橙色
                        else -> AccentGreen
                    }
                    
                    Box(
                        modifier = Modifier.size(28.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(id = batteryIconId),
                            contentDescription = "电池状态 $batteryPercentage%",
                            tint = batteryColor,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    
                    // 电池百分比
                    Text(
                        text = "$batteryPercentage%",
                        fontSize = 12.sp,
                        color = batteryColor,
                        textAlign = TextAlign.Center
                    )
                }
                
                // 连接状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(end = 12.dp)
                ) {
                    // 状态图标
                    Box(
                        modifier = Modifier
                            .size(28.dp)
                            .clip(CircleShape)
                            .background(if (isConnected) ConnectedGreen else DisconnectedRed),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_bluetooth),
                            contentDescription = "连接状态",
                            tint = Color.White,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    // 状态文本
                    Text(
                        text = if (isConnected) "已连接" else "未连接",
                        fontSize = 12.sp,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
                
                // LED灯控制
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // LED开关
                    Switch(
                        checked = ledStatus,
                        onCheckedChange = { onLedToggle() },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = AccentBlue,
                            checkedTrackColor = AccentBlue.copy(alpha = 0.5f),
                            uncheckedThumbColor = Color.White,
                            uncheckedTrackColor = Color.Gray.copy(alpha = 0.5f)
                        ),
                        modifier = Modifier.size(width = 40.dp, height = 24.dp)
                    )
                    
                    // LED标签
                    Text(
                        text = "LED灯",
                        fontSize = 12.sp,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
} 