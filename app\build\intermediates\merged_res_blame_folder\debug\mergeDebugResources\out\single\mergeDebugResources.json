[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_screenshot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_screenshot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-pngs-42:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_direction_controls.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_direction_controls.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\raw_beep.MP3.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\raw\\beep.MP3"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_arrow_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_arrow_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_zhong.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\zhong.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_vibration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_vibration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_battery.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_battery.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_battery_75.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_battery_75.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_wifi.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_wifi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_arrow_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_arrow_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_xiao.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\xiao.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_xiao_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\xiao_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_arrow_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_arrow_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_battery_50.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_battery_50.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_da.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\da.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_da_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\da_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_battery_low.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_battery_low.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_zhong_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\zhong_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_depth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_depth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_temperature.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_temperature.xml"}, {"merged": "com.example.lnnfilsh.app-debug-46:/drawable_ic_radar.xml.flat", "source": "com.example.lnnfilsh.app-main-48:/drawable/ic_radar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_battery_full.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_battery_full.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_bluetooth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_bluetooth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_battery_25.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_battery_25.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-debug-46:\\drawable_ic_fish.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.lnnfilsh.app-main-48:\\drawable\\ic_fish.xml"}]