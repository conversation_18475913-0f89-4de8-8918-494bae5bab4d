package com.example.lnnfilsh

import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.example.lnnfilsh.ui.screens.MainScreen
import com.example.lnnfilsh.ui.theme.LnnfilshTheme
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    companion object {
        private const val TAG = "MainActivity"
    }
    
    private lateinit var windowInsetsController: WindowInsetsControllerCompat
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // 全屏显示 - 隐藏系统状态栏
            WindowCompat.setDecorFitsSystemWindows(window, false)
            windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
            windowInsetsController.hide(WindowInsetsCompat.Type.statusBars())
            windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            
            // 保持屏幕常亮
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            
            // 设置异常处理器
            setupExceptionHandler()
            
            // 使用生命周期感知的协程
            lifecycleScope.launch {
                repeatOnLifecycle(Lifecycle.State.CREATED) {
                    setContent {
                        LnnfilshTheme {
                            Surface(
                                modifier = Modifier.fillMaxSize(),
                                color = MaterialTheme.colorScheme.background
                            ) {
                                MainScreen()
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "onCreate发生错误: ${e.message}", e)
        }
    }
    
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        try {
            // 横屏模式下隐藏导航栏
            if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                windowInsetsController.hide(WindowInsetsCompat.Type.navigationBars())
            } else {
                // 竖屏模式下可以显示导航栏
                windowInsetsController.show(WindowInsetsCompat.Type.navigationBars())
            }
        } catch (e: Exception) {
            Log.e(TAG, "配置更改处理出错: ${e.message}", e)
        }
    }
    
    private fun setupExceptionHandler() {
        val defaultExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            try {
                Log.e(TAG, "捕获到未处理异常: ${throwable.message}", throwable)
                // 这里可以添加崩溃日志上报等功能
            } catch (e: Exception) {
                Log.e(TAG, "处理异常时出错: ${e.message}", e)
            } finally {
                // 调用原始的异常处理器
                defaultExceptionHandler?.uncaughtException(thread, throwable)
            }
        }
    }
    
    override fun onDestroy() {
        try {
            // 清理资源
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } catch (e: Exception) {
            Log.e(TAG, "onDestroy发生错误: ${e.message}", e)
        }
        super.onDestroy()
    }
} 