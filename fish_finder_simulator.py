import socket
import time
import random
import struct
import threading
import datetime
import sys
import signal
import binascii  # 添加binascii模块用于调试

# 配置参数
HOST = '0.0.0.0'  # 监听所有接口
PORT = 8266       # 默认端口
FRAME_HEADER = b'\xAA\xBB'  # 上行数据帧头
CMD_HEADER = b'\xCC\xDD'    # 下行命令帧头

# 设备状态变量
device_status = {
    # 是否开启采集
    'collecting': True,

    # 是否开启LED
    'led_status': False,
    # 电量
    'battery': 15,
    # 灵敏度
    'sensitivity': 5,
    # 采样间隔
    'sampling_interval': 50,  # 500毫秒
    
    # 运动控制状态
    'moving_forward': False, # 前进
    'moving_backward': False, # 后退
    'turning_left': False, # 左转
    'turning_right': False, # 右转
    'moving_speed': 5         # 默认速度值
}

# 模拟数据配置
simulate_config = {
    'enable_ultrasonic': True,  # 启用超声波模拟
    'ultrasonic_period': 3,     # 超声波数据发送周期(秒)
    'random_depth_change': 5,  # 深度随机变化范围(±厘米)
    'debug_mode': True          # 调试模式
}

# 日志函数
def log(message):
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

# CRC16-MODBUS校验
def calculate_crc16(data):
    crc = 0xFFFF
    for i in range(len(data)):
        crc ^= data[i]
        for _ in range(8):
            if crc & 0x0001:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc = crc >> 1
    return crc.to_bytes(2, byteorder='little')

# 生成深度数据帧
def create_depth_frame():
    # 命令类型：深度数据
    cmd_type = 0x01
    # 数据长度
    data_len = 14
    
    # 随机生成深度数据 (1m - 2.5m)
    depth = int(random.uniform(250, 500))
    # 随机生成水温 (15°C - 25°C)
    temp = int(random.uniform(230, 250))
    
    # 构建数据内容
    data_content = struct.pack('<HH', depth, temp) + bytes(10)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame, depth, temp

# 生成超声波探测数据帧
def create_ultrasonic_frame(base_depth=200):
    # 命令类型：超声波数据
    cmd_type = 0x05
    # 数据长度
    data_len = 14
    
    # 生成一组超声波探测数据（8个点）
    ultrasonic_data = bytearray(8)
    
    # 基础深度值附近随机波动
    for i in range(8):
        # 生成-10到+10厘米的随机波动
        variation = random.randint(-simulate_config['random_depth_change'], simulate_config['random_depth_change'])
        depth_point = base_depth + variation
        # 确保深度值在有效范围内
        depth_point = max(50, min(500, depth_point))
        ultrasonic_data[i] = int(depth_point / 5)  # 压缩数据范围，每单位5厘米
    
    # 构建数据内容
    data_content = bytes(ultrasonic_data) + bytes(6)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame, ultrasonic_data

# 生成状态数据帧
def create_status_frame():
    # 命令类型：状态数据
    cmd_type = 0x02
    # 数据长度
    data_len = 14
    
    # 构建状态字节
    status_byte = 0
    if device_status['collecting']:
        status_byte |= 0x01
    if device_status['led_status']:
        status_byte |= 0x02
    
    # 运动状态
    motion_byte = 0
    if device_status['moving_forward']:
        motion_byte |= 0x01
    if device_status['moving_backward']:
        motion_byte |= 0x02
    if device_status['turning_left']:
        motion_byte |= 0x04
    if device_status['turning_right']:
        motion_byte |= 0x08
    
    # 构建数据内容
    data_content = bytes([device_status['battery'], status_byte, motion_byte, device_status['moving_speed']]) + bytes(10)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame

# 生成鱼群数据帧
def create_fish_frame():
    # 命令类型：鱼群数据
    cmd_type = 0x03
    # 数据长度
    data_len = 14
    
    # 随机生成鱼的深度 (0.5m - 3m)
    fish_depth = int(random.uniform(100, 450))
    # 随机生成鱼的大小 (1-小, 2-中, 3-大)
    fish_size = random.randint(1, 3)
    # 随机生成置信度 (60% - 95%)
    confidence = random.randint(60, 95)
    
    # 构建数据内容
    data_content = struct.pack('<HBB', fish_depth, fish_size, confidence) + bytes(10)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame, fish_depth, fish_size, confidence

# 解析命令帧
def parse_command(data):
    # 检查帧头
    if data[0:2] != CMD_HEADER:
        log(f"无效的命令帧头: {data[0:2].hex()}, 应为: {CMD_HEADER.hex()}")
        return None
    
    # 提取命令类型
    cmd_type = data[2]
    # 提取参数长度
    param_len = data[3]
    # 提取参数内容
    params = data[4:4+param_len]
    
    if simulate_config['debug_mode']:
        log(f"命令详情: 类型=0x{cmd_type:02X}, 参数长度={param_len}, 参数={binascii.hexlify(params).decode()}")
    
    # 验证CRC
    received_crc = data[8:10]
    calculated_crc = calculate_crc16(data[2:8])
    if received_crc != calculated_crc:
        log(f"CRC校验失败 - 接收: {received_crc.hex()}, 计算: {calculated_crc.hex()}")
        return None
    
    # 处理不同类型的命令
    if cmd_type == 0x01:  # 开始采集
        device_status['collecting'] = True
        log(f"收到命令: 开始采集 [类型: 0x{cmd_type:02X}]")
        
    elif cmd_type == 0x02:  # 停止采集
        # 不再允许停止采集，始终保持开启状态
        log(f"收到命令: 停止采集 [类型: 0x{cmd_type:02X}] - 已忽略，保持采集状态")
        
    elif cmd_type == 0x03:  # 设置参数
        if param_len >= 2:
            device_status['sensitivity'] = params[0]
            device_status['sampling_interval'] = params[1]
            log(f"收到命令: 设置参数 [类型: 0x{cmd_type:02X}] (灵敏度={params[0]}, 采样间隔={params[1]*10}ms)")
            
    elif cmd_type == 0x04:  # 切换LED
        device_status['led_status'] = not device_status['led_status']
        led_status_str = '开启' if device_status['led_status'] else '关闭'
        log(f"收到命令: 切换LED [类型: 0x{cmd_type:02X}] (当前状态={led_status_str})")
        
    elif cmd_type == 0x05:  # 重置设备
        device_status['collecting'] = True  # 重置后默认开启采集
        device_status['led_status'] = False
        device_status['sensitivity'] = 5
        device_status['sampling_interval'] = 50
        # 重置运动状态
        device_status['moving_forward'] = False
        device_status['moving_backward'] = False
        device_status['turning_left'] = False
        device_status['turning_right'] = False
        device_status['moving_speed'] = 5
        log(f"收到命令: 重置设备 [类型: 0x{cmd_type:02X}]")
    
    elif cmd_type == 0x06:  # 前进
        # 重置其他运动状态
        device_status['moving_backward'] = False
        device_status['turning_left'] = False
        device_status['turning_right'] = False
        # 设置前进状态
        device_status['moving_forward'] = True
        if param_len >= 1:
            device_status['moving_speed'] = params[0]
        log(f"收到命令: 前进 [类型: 0x{cmd_type:02X}] (速度={device_status['moving_speed']})")
    
    elif cmd_type == 0x07:  # 后退
        # 重置其他运动状态
        device_status['moving_forward'] = False
        device_status['turning_left'] = False
        device_status['turning_right'] = False
        # 设置后退状态
        device_status['moving_backward'] = True
        if param_len >= 1:
            device_status['moving_speed'] = params[0]
        log(f"收到命令: 后退 [类型: 0x{cmd_type:02X}] (速度={device_status['moving_speed']})")
    
    elif cmd_type == 0x08:  # 左转
        # 重置其他运动状态
        device_status['moving_forward'] = False
        device_status['moving_backward'] = False
        device_status['turning_right'] = False
        # 设置左转状态
        device_status['turning_left'] = True
        if param_len >= 1:
            device_status['moving_speed'] = params[0]
        log(f"收到命令: 左转 [类型: 0x{cmd_type:02X}] (速度={device_status['moving_speed']})")
    
    elif cmd_type == 0x09:  # 右转
        # 重置其他运动状态
        device_status['moving_forward'] = False
        device_status['moving_backward'] = False
        device_status['turning_left'] = False
        # 设置右转状态
        device_status['turning_right'] = True
        if param_len >= 1:
            device_status['moving_speed'] = params[0]
        log(f"收到命令: 右转 [类型: 0x{cmd_type:02X}] (速度={device_status['moving_speed']})")
    
    elif cmd_type == 0x0A:  # 停止
        # 重置所有运动状态
        device_status['moving_forward'] = False
        device_status['moving_backward'] = False
        device_status['turning_left'] = False
        device_status['turning_right'] = False
        log(f"收到命令: 停止 [类型: 0x{cmd_type:02X}]")
    
    else:
        log(f"收到未知命令类型: 0x{cmd_type:02X}")
        return None
    
    # 生成响应
    return create_response_frame(cmd_type)

# 生成响应帧
def create_response_frame(original_cmd):
    # 命令类型：响应命令
    cmd_type = 0x04
    # 数据长度
    data_len = 14
    
    # 构建数据内容 (第一个字节是原始命令类型)
    data_content = bytes([original_cmd]) + bytes(13)
    
    # 构建完整帧
    frame = FRAME_HEADER + bytes([cmd_type, data_len]) + data_content
    # 添加CRC校验
    frame += calculate_crc16(frame[2:18])
    
    return frame

# 数据发送线程
def data_sender(conn, addr):
    try:
        # 强制设置采集状态为开启
        device_status['collecting'] = True
        
        # 首先发送状态帧确认连接
        conn.sendall(create_status_frame())
        log(f"已发送初始状态帧 -> {addr[0]}:{addr[1]} [电量={device_status['battery']}%, 采集={device_status['collecting']}, LED={device_status['led_status']}]")
        
        last_status_time = time.time()
        last_ultrasonic_time = time.time() - simulate_config['ultrasonic_period']  # 立即发送第一帧
        base_depth = 400  # 基础深度，单位：厘米
        
        # 立即发送一次超声波数据，确保连接后立即有数据
        ultrasonic_frame, data_points = create_ultrasonic_frame(base_depth)
        conn.sendall(ultrasonic_frame)
        formatted_points = [f"{p*5/100:.2f}m" for p in data_points]
        visual = "【" + " ".join([str(int(p*5)) + "cm" for p in data_points]) + "】"
        log(f"已发送初始超声波探测数据 -> {addr[0]}:{addr[1]}")
        log(f"    数据点: {', '.join(formatted_points)}")
        log(f"    可视化: {visual}")
        
        while True:
            current_time = time.time()
            
            # 不管collecting状态，都发送数据（强制采集模式）
            # 发送深度数据帧
            depth_frame, depth, temp = create_depth_frame()
            conn.sendall(depth_frame)
            log(f"已发送深度数据帧 -> {addr[0]}:{addr[1]} [深度={depth/100:.2f}m, 水温={temp/10:.1f}°C]")
            
            # 发送超声波探测数据 - 提高发送频率，始终发送
            if simulate_config['enable_ultrasonic'] and (current_time - last_ultrasonic_time >= simulate_config['ultrasonic_period']):
                # 随机更新基础深度
                base_depth = base_depth + random.randint(-10, 10)
                base_depth = max(150, min(320, base_depth))  # 保持在1-3米范围内
                
                ultrasonic_frame, data_points = create_ultrasonic_frame(base_depth)
                conn.sendall(ultrasonic_frame)
                
                # 格式化数据点为易读形式，添加更多视觉化
                formatted_points = [f"{p*5/100:.2f}m" for p in data_points]
                visual = "【" + " ".join([str(int(p*5)) + "cm" for p in data_points]) + "】"
                log(f"已发送超声波探测数据 -> {addr[0]}:{addr[1]}")
                log(f"    数据点: {', '.join(formatted_points)}")
                log(f"    可视化: {visual}")
                last_ultrasonic_time = current_time
            
            # 随机发送鱼群数据 (20%概率)
            if random.random() < 0.2:
                fish_frame, fish_depth, fish_size, confidence = create_fish_frame()
                conn.sendall(fish_frame)
                fish_size_desc = {1: "小", 2: "中", 3: "大"}[fish_size]
                log(f"已发送鱼群数据帧 -> {addr[0]}:{addr[1]} [深度={fish_depth/100:.2f}m, 大小={fish_size_desc}({fish_size}), 置信度={confidence}%]")
            
            # 每5秒发送一次状态帧，或者LED状态改变时立即发送
            if current_time - last_status_time >= 5:
                conn.sendall(create_status_frame())
                led_status_str = '开启' if device_status['led_status'] else '关闭'
                log(f"已发送状态数据帧 -> {addr[0]}:{addr[1]} [电量={device_status['battery']}%, 采集={device_status['collecting']}, LED={led_status_str}]")
                last_status_time = current_time
            
            # 根据采样间隔延时
            time.sleep(device_status['sampling_interval'] * 0.01)
            
    except Exception as e:
        log(f"发送数据时出错: {e}")

# 信号处理函数
def signal_handler(sig, frame):
    log("接收到退出信号 (Ctrl+C)，正在关闭服务器...")
    sys.exit(0)

# 主函数
def main():
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    # 创建TCP服务器
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    try:
        server_socket.bind((HOST, PORT))
        server_socket.listen(5)  # 增加等待连接队列
        
        # 获取本机所有IP地址
        hostname = socket.gethostname()
        ip_list = socket.gethostbyname_ex(hostname)[2]
        
        log(f"ESP8266模拟器已启动")
        log(f"监听所有网络接口 (0.0.0.0) 端口 {PORT}")
        log(f"本机IP地址:")
        for ip in ip_list:
            log(f" - {ip}:{PORT}")
        log(f"采集状态: {'开启' if device_status['collecting'] else '关闭'}")
        log(f"超声波模拟: {'开启' if simulate_config['enable_ultrasonic'] else '关闭'}")
        log(f"调试模式: {'开启' if simulate_config['debug_mode'] else '关闭'}")
        log("等待设备连接...")
        log("按 Ctrl+C 可随时退出程序")
        
        # 连接处理线程列表
        client_threads = []
        
        while True:
            # 等待连接
            conn, addr = server_socket.accept()
            log(f"✓ 连接成功！客户端IP: {addr[0]}:{addr[1]}")
            
            # 创建一个新线程处理这个客户端
            client_thread = threading.Thread(target=handle_client, args=(conn, addr))
            client_thread.daemon = True
            client_thread.start()
            
            # 保存线程引用并清理已结束的线程
            client_threads.append(client_thread)
            client_threads = [t for t in client_threads if t.is_alive()]
            
            log(f"当前活跃连接数: {len(client_threads)}")
    
    except KeyboardInterrupt:
        log("程序被用户中断")
    
    except Exception as e:
        log(f"服务器错误: {e}")
    
    finally:
        server_socket.close()
        log("服务器已关闭")

# 客户端处理函数
def handle_client(conn, addr):
    try:
        # 启动数据发送线程
        sender_thread = threading.Thread(target=data_sender, args=(conn, addr))
        sender_thread.daemon = True
        sender_thread.start()
        
        # 接收命令
        while True:
            try:
                # 接收命令数据
                data = conn.recv(10)  # 命令帧固定长度为10字节
                if not data:
                    log(f"客户端 {addr[0]}:{addr[1]} 断开连接")
                    break
                
                # 检查数据是否是JSON格式（旧版本命令）
                if data[0:1] == b'{':
                    log(f"收到JSON格式命令 (旧版本): {data[:20]}...")
                    # 这里不处理JSON命令，直接忽略
                    continue
                
                # 处理二进制命令
                if simulate_config['debug_mode']:
                    log(f"接收到命令帧: {binascii.hexlify(data).decode()}")
                else:
                    log(f"接收到命令帧: {data.hex()}")
                
                # 解析并处理命令
                response = parse_command(data)
                if response:
                    # 发送响应
                    conn.sendall(response)
                    log(f"已发送响应帧 -> {addr[0]}:{addr[1]} [响应类型: 0x04]")
                    
                    # 命令执行后立即发送更新的状态帧
                    # 对于LED命令或运动控制命令，状态变化后需要立即反馈
                    cmd_type = data[2]
                    if cmd_type in [0x04, 0x06, 0x07, 0x08, 0x09, 0x0A]:
                        time.sleep(0.1)  # 稍微延迟，确保响应帧先发送
                        status_frame = create_status_frame()
                        conn.sendall(status_frame)
                        
                        if cmd_type == 0x04:
                            led_status_str = '开启' if device_status['led_status'] else '关闭'
                            log(f"LED状态更新 - 已发送状态数据帧 -> {addr[0]}:{addr[1]} [LED={led_status_str}]")
                        else:
                            motion_status = []
                            if device_status['moving_forward']: motion_status.append("前进")
                            if device_status['moving_backward']: motion_status.append("后退")
                            if device_status['turning_left']: motion_status.append("左转")
                            if device_status['turning_right']: motion_status.append("右转")
                            
                            motion_str = "静止" if not motion_status else "、".join(motion_status)
                            log(f"运动状态更新 - 已发送状态数据帧 -> {addr[0]}:{addr[1]} [状态={motion_str}, 速度={device_status['moving_speed']}]")
            
            except socket.timeout:
                # 超时，继续尝试接收
                continue
                
            except Exception as e:
                log(f"接收命令时出错: {e}")
                break
    
    except ConnectionResetError:
        log(f"连接被客户端重置: {addr[0]}:{addr[1]}")
    
    except ConnectionAbortedError:
        log(f"连接被客户端中止: {addr[0]}:{addr[1]}")
    
    except Exception as e:
        log(f"处理客户端数据时出错: {e}")
    
    finally:
        try:
            conn.close()
            log(f"客户端 {addr[0]}:{addr[1]} 连接已关闭")
        except:
            pass

if __name__ == "__main__":
    main() 